[{"label": "numpy", "kind": 6, "isExtraImport": true, "importPath": "numpy", "description": "numpy", "detail": "numpy", "documentation": {}}, {"label": "torch", "kind": 6, "isExtraImport": true, "importPath": "torch", "description": "torch", "detail": "torch", "documentation": {}}, {"label": "Dataset", "importPath": "torch.utils.data", "description": "torch.utils.data", "isExtraImport": true, "detail": "torch.utils.data", "documentation": {}}, {"label": "DataLoader", "importPath": "torch.utils.data", "description": "torch.utils.data", "isExtraImport": true, "detail": "torch.utils.data", "documentation": {}}, {"label": "Dataset", "importPath": "torch.utils.data", "description": "torch.utils.data", "isExtraImport": true, "detail": "torch.utils.data", "documentation": {}}, {"label": "DataLoader", "importPath": "torch.utils.data", "description": "torch.utils.data", "isExtraImport": true, "detail": "torch.utils.data", "documentation": {}}, {"label": "DataLoader", "importPath": "torch.utils.data", "description": "torch.utils.data", "isExtraImport": true, "detail": "torch.utils.data", "documentation": {}}, {"label": "TensorDataset", "importPath": "torch.utils.data", "description": "torch.utils.data", "isExtraImport": true, "detail": "torch.utils.data", "documentation": {}}, {"label": "DataLoader", "importPath": "torch.utils.data", "description": "torch.utils.data", "isExtraImport": true, "detail": "torch.utils.data", "documentation": {}}, {"label": "TensorDataset", "importPath": "torch.utils.data", "description": "torch.utils.data", "isExtraImport": true, "detail": "torch.utils.data", "documentation": {}}, {"label": "DataLoader", "importPath": "torch.utils.data", "description": "torch.utils.data", "isExtraImport": true, "detail": "torch.utils.data", "documentation": {}}, {"label": "TensorDataset", "importPath": "torch.utils.data", "description": "torch.utils.data", "isExtraImport": true, "detail": "torch.utils.data", "documentation": {}}, {"label": "DataLoader", "importPath": "torch.utils.data", "description": "torch.utils.data", "isExtraImport": true, "detail": "torch.utils.data", "documentation": {}}, {"label": "TensorDataset", "importPath": "torch.utils.data", "description": "torch.utils.data", "isExtraImport": true, "detail": "torch.utils.data", "documentation": {}}, {"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "json", "kind": 6, "isExtraImport": true, "importPath": "json", "description": "json", "detail": "json", "documentation": {}}, {"label": "pickle", "kind": 6, "isExtraImport": true, "importPath": "pickle", "description": "pickle", "detail": "pickle", "documentation": {}}, {"label": "shutil", "kind": 6, "isExtraImport": true, "importPath": "shutil", "description": "shutil", "detail": "shutil", "documentation": {}}, {"label": "logging", "kind": 6, "isExtraImport": true, "importPath": "logging", "description": "logging", "detail": "logging", "documentation": {}}, {"label": "r2_score", "importPath": "sklearn.metrics", "description": "sklearn.metrics", "isExtraImport": true, "detail": "sklearn.metrics", "documentation": {}}, {"label": "mean_squared_error", "importPath": "sklearn.metrics", "description": "sklearn.metrics", "isExtraImport": true, "detail": "sklearn.metrics", "documentation": {}}, {"label": "mean_absolute_error", "importPath": "sklearn.metrics", "description": "sklearn.metrics", "isExtraImport": true, "detail": "sklearn.metrics", "documentation": {}}, {"label": "r2_score", "importPath": "sklearn.metrics", "description": "sklearn.metrics", "isExtraImport": true, "detail": "sklearn.metrics", "documentation": {}}, {"label": "mean_squared_error", "importPath": "sklearn.metrics", "description": "sklearn.metrics", "isExtraImport": true, "detail": "sklearn.metrics", "documentation": {}}, {"label": "mean_absolute_error", "importPath": "sklearn.metrics", "description": "sklearn.metrics", "isExtraImport": true, "detail": "sklearn.metrics", "documentation": {}}, {"label": "r2_score", "importPath": "sklearn.metrics", "description": "sklearn.metrics", "isExtraImport": true, "detail": "sklearn.metrics", "documentation": {}}, {"label": "mean_squared_error", "importPath": "sklearn.metrics", "description": "sklearn.metrics", "isExtraImport": true, "detail": "sklearn.metrics", "documentation": {}}, {"label": "mean_absolute_error", "importPath": "sklearn.metrics", "description": "sklearn.metrics", "isExtraImport": true, "detail": "sklearn.metrics", "documentation": {}}, {"label": "r2_score", "importPath": "sklearn.metrics", "description": "sklearn.metrics", "isExtraImport": true, "detail": "sklearn.metrics", "documentation": {}}, {"label": "r2_score", "importPath": "sklearn.metrics", "description": "sklearn.metrics", "isExtraImport": true, "detail": "sklearn.metrics", "documentation": {}}, {"label": "r2_score", "importPath": "sklearn.metrics", "description": "sklearn.metrics", "isExtraImport": true, "detail": "sklearn.metrics", "documentation": {}}, {"label": "mean_squared_error", "importPath": "sklearn.metrics", "description": "sklearn.metrics", "isExtraImport": true, "detail": "sklearn.metrics", "documentation": {}}, {"label": "mean_absolute_error", "importPath": "sklearn.metrics", "description": "sklearn.metrics", "isExtraImport": true, "detail": "sklearn.metrics", "documentation": {}}, {"label": "pear<PERSON><PERSON>", "importPath": "scipy.stats", "description": "scipy.stats", "isExtraImport": true, "detail": "scipy.stats", "documentation": {}}, {"label": "spearmanr", "importPath": "scipy.stats", "description": "scipy.stats", "isExtraImport": true, "detail": "scipy.stats", "documentation": {}}, {"label": "pear<PERSON><PERSON>", "importPath": "scipy.stats", "description": "scipy.stats", "isExtraImport": true, "detail": "scipy.stats", "documentation": {}}, {"label": "spearmanr", "importPath": "scipy.stats", "description": "scipy.stats", "isExtraImport": true, "detail": "scipy.stats", "documentation": {}}, {"label": "pear<PERSON><PERSON>", "importPath": "scipy.stats", "description": "scipy.stats", "isExtraImport": true, "detail": "scipy.stats", "documentation": {}}, {"label": "spearmanr", "importPath": "scipy.stats", "description": "scipy.stats", "isExtraImport": true, "detail": "scipy.stats", "documentation": {}}, {"label": "pear<PERSON><PERSON>", "importPath": "scipy.stats", "description": "scipy.stats", "isExtraImport": true, "detail": "scipy.stats", "documentation": {}}, {"label": "pear<PERSON><PERSON>", "importPath": "scipy.stats", "description": "scipy.stats", "isExtraImport": true, "detail": "scipy.stats", "documentation": {}}, {"label": "matplotlib.pyplot", "kind": 6, "isExtraImport": true, "importPath": "matplotlib.pyplot", "description": "matplotlib.pyplot", "detail": "matplotlib.pyplot", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "torch.nn", "kind": 6, "isExtraImport": true, "importPath": "torch.nn", "description": "torch.nn", "detail": "torch.nn", "documentation": {}}, {"label": "ParameterGrid", "importPath": "sklearn.model_selection", "description": "sklearn.model_selection", "isExtraImport": true, "detail": "sklearn.model_selection", "documentation": {}}, {"label": "ParameterGrid", "importPath": "sklearn.model_selection", "description": "sklearn.model_selection", "isExtraImport": true, "detail": "sklearn.model_selection", "documentation": {}}, {"label": "train_test_split", "importPath": "sklearn.model_selection", "description": "sklearn.model_selection", "isExtraImport": true, "detail": "sklearn.model_selection", "documentation": {}}, {"label": "train_test_split", "importPath": "sklearn.model_selection", "description": "sklearn.model_selection", "isExtraImport": true, "detail": "sklearn.model_selection", "documentation": {}}, {"label": "cross_val_score", "importPath": "sklearn.model_selection", "description": "sklearn.model_selection", "isExtraImport": true, "detail": "sklearn.model_selection", "documentation": {}}, {"label": "NPYDataset", "importPath": "utils.dataset", "description": "utils.dataset", "isExtraImport": true, "detail": "utils.dataset", "documentation": {}}, {"label": "NPYDataset", "importPath": "utils.dataset", "description": "utils.dataset", "isExtraImport": true, "detail": "utils.dataset", "documentation": {}}, {"label": "calculate_metrics", "importPath": "utils.metrics", "description": "utils.metrics", "isExtraImport": true, "detail": "utils.metrics", "documentation": {}}, {"label": "calculate_metrics", "importPath": "utils.metrics", "description": "utils.metrics", "isExtraImport": true, "detail": "utils.metrics", "documentation": {}}, {"label": "calculate_metrics", "importPath": "utils.metrics", "description": "utils.metrics", "isExtraImport": true, "detail": "utils.metrics", "documentation": {}}, {"label": "create_results_folder", "importPath": "utils.save_results", "description": "utils.save_results", "isExtraImport": true, "detail": "utils.save_results", "documentation": {}}, {"label": "save_metrics_and_results", "importPath": "utils.save_results", "description": "utils.save_results", "isExtraImport": true, "detail": "utils.save_results", "documentation": {}}, {"label": "plot_and_save", "importPath": "utils.save_results", "description": "utils.save_results", "isExtraImport": true, "detail": "utils.save_results", "documentation": {}}, {"label": "create_results_folder", "importPath": "utils.save_results", "description": "utils.save_results", "isExtraImport": true, "detail": "utils.save_results", "documentation": {}}, {"label": "save_metrics_and_results", "importPath": "utils.save_results", "description": "utils.save_results", "isExtraImport": true, "detail": "utils.save_results", "documentation": {}}, {"label": "plot_and_save", "importPath": "utils.save_results", "description": "utils.save_results", "isExtraImport": true, "detail": "utils.save_results", "documentation": {}}, {"label": "plot_and_save", "importPath": "utils.save_results", "description": "utils.save_results", "isExtraImport": true, "detail": "utils.save_results", "documentation": {}}, {"label": "create_results_folder", "importPath": "utils.save_results", "description": "utils.save_results", "isExtraImport": true, "detail": "utils.save_results", "documentation": {}}, {"label": "save_best_model", "importPath": "utils.helpers", "description": "utils.helpers", "isExtraImport": true, "detail": "utils.helpers", "documentation": {}}, {"label": "save_best_model", "importPath": "utils.helpers", "description": "utils.helpers", "isExtraImport": true, "detail": "utils.helpers", "documentation": {}}, {"label": "setup_logger", "importPath": "utils.logger", "description": "utils.logger", "isExtraImport": true, "detail": "utils.logger", "documentation": {}}, {"label": "setup_logger", "importPath": "utils.logger", "description": "utils.logger", "isExtraImport": true, "detail": "utils.logger", "documentation": {}}, {"label": "TransformerRegressor", "importPath": "utils.transformer_regressor", "description": "utils.transformer_regressor", "isExtraImport": true, "detail": "utils.transformer_regressor", "documentation": {}}, {"label": "TransformerRegressor", "importPath": "utils.transformer_regressor", "description": "utils.transformer_regressor", "isExtraImport": true, "detail": "utils.transformer_regressor", "documentation": {}}, {"label": "TransformerRegressor", "importPath": "utils.transformer_regressor", "description": "utils.transformer_regressor", "isExtraImport": true, "detail": "utils.transformer_regressor", "documentation": {}}, {"label": "random", "kind": 6, "isExtraImport": true, "importPath": "random", "description": "random", "detail": "random", "documentation": {}}, {"label": "pandas", "kind": 6, "isExtraImport": true, "importPath": "pandas", "description": "pandas", "detail": "pandas", "documentation": {}}, {"label": "StandardScaler", "importPath": "sklearn.preprocessing", "description": "sklearn.preprocessing", "isExtraImport": true, "detail": "sklearn.preprocessing", "documentation": {}}, {"label": "RobustScaler", "importPath": "sklearn.preprocessing", "description": "sklearn.preprocessing", "isExtraImport": true, "detail": "sklearn.preprocessing", "documentation": {}}, {"label": "PowerTransformer", "importPath": "sklearn.preprocessing", "description": "sklearn.preprocessing", "isExtraImport": true, "detail": "sklearn.preprocessing", "documentation": {}}, {"label": "PolynomialFeatures", "importPath": "sklearn.preprocessing", "description": "sklearn.preprocessing", "isExtraImport": true, "detail": "sklearn.preprocessing", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "importPath": "sklearn.feature_selection", "description": "sklearn.feature_selection", "isExtraImport": true, "detail": "sklearn.feature_selection", "documentation": {}}, {"label": "SelectKBest", "importPath": "sklearn.feature_selection", "description": "sklearn.feature_selection", "isExtraImport": true, "detail": "sklearn.feature_selection", "documentation": {}}, {"label": "f_regression", "importPath": "sklearn.feature_selection", "description": "sklearn.feature_selection", "isExtraImport": true, "detail": "sklearn.feature_selection", "documentation": {}}, {"label": "mutual_info_regression", "importPath": "sklearn.feature_selection", "description": "sklearn.feature_selection", "isExtraImport": true, "detail": "sklearn.feature_selection", "documentation": {}}, {"label": "PCA", "importPath": "sklearn.decomposition", "description": "sklearn.decomposition", "isExtraImport": true, "detail": "sklearn.decomposition", "documentation": {}}, {"label": "PCA", "importPath": "sklearn.decomposition", "description": "sklearn.decomposition", "isExtraImport": true, "detail": "sklearn.decomposition", "documentation": {}}, {"label": "FastICA", "importPath": "sklearn.decomposition", "description": "sklearn.decomposition", "isExtraImport": true, "detail": "sklearn.decomposition", "documentation": {}}, {"label": "seaborn", "kind": 6, "isExtraImport": true, "importPath": "seaborn", "description": "seaborn", "detail": "seaborn", "documentation": {}}, {"label": "torch.nn.functional", "kind": 6, "isExtraImport": true, "importPath": "torch.nn.functional", "description": "torch.nn.functional", "detail": "torch.nn.functional", "documentation": {}}, {"label": "math", "kind": 6, "isExtraImport": true, "importPath": "math", "description": "math", "detail": "math", "documentation": {}}, {"label": "torch.optim", "kind": 6, "isExtraImport": true, "importPath": "torch.optim", "description": "torch.optim", "detail": "torch.optim", "documentation": {}}, {"label": "CosineAnnealingLR", "importPath": "torch.optim.lr_scheduler", "description": "torch.optim.lr_scheduler", "isExtraImport": true, "detail": "torch.optim.lr_scheduler", "documentation": {}}, {"label": "ReduceLROnPlateau", "importPath": "torch.optim.lr_scheduler", "description": "torch.optim.lr_scheduler", "isExtraImport": true, "detail": "torch.optim.lr_scheduler", "documentation": {}}, {"label": "OneCycleLR", "importPath": "torch.optim.lr_scheduler", "description": "torch.optim.lr_scheduler", "isExtraImport": true, "detail": "torch.optim.lr_scheduler", "documentation": {}}, {"label": "OneCycleLR", "importPath": "torch.optim.lr_scheduler", "description": "torch.optim.lr_scheduler", "isExtraImport": true, "detail": "torch.optim.lr_scheduler", "documentation": {}}, {"label": "CosineAnnealingLR", "importPath": "torch.optim.lr_scheduler", "description": "torch.optim.lr_scheduler", "isExtraImport": true, "detail": "torch.optim.lr_scheduler", "documentation": {}}, {"label": "warnings", "kind": 6, "isExtraImport": true, "importPath": "warnings", "description": "warnings", "detail": "warnings", "documentation": {}}, {"label": "create_model", "importPath": "advanced_models", "description": "advanced_models", "isExtraImport": true, "detail": "advanced_models", "documentation": {}}, {"label": "EnsembleRegressor", "importPath": "advanced_models", "description": "advanced_models", "isExtraImport": true, "detail": "advanced_models", "documentation": {}}, {"label": "ast", "kind": 6, "isExtraImport": true, "importPath": "ast", "description": "ast", "detail": "ast", "documentation": {}}, {"label": "RandomForestRegressor", "importPath": "sklearn.ensemble", "description": "sklearn.ensemble", "isExtraImport": true, "detail": "sklearn.ensemble", "documentation": {}}, {"label": "GradientBoostingRegressor", "importPath": "sklearn.ensemble", "description": "sklearn.ensemble", "isExtraImport": true, "detail": "sklearn.ensemble", "documentation": {}}, {"label": "ElasticNet", "importPath": "sklearn.linear_model", "description": "sklearn.linear_model", "isExtraImport": true, "detail": "sklearn.linear_model", "documentation": {}}, {"label": "Ridge", "importPath": "sklearn.linear_model", "description": "sklearn.linear_model", "isExtraImport": true, "detail": "sklearn.linear_model", "documentation": {}}, {"label": "xgboost", "kind": 6, "isExtraImport": true, "importPath": "xgboost", "description": "xgboost", "detail": "xgboost", "documentation": {}}, {"label": "lightgbm", "kind": 6, "isExtraImport": true, "importPath": "lightgbm", "description": "lightgbm", "detail": "lightgbm", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "NPYDataset", "kind": 6, "importPath": "TransCode.utils.dataset", "description": "TransCode.utils.dataset", "peekOfCode": "class NPYDataset(Dataset):\n    def __init__(self, features_file, labels_file):\n        self.features = np.load(features_file)\n        self.labels = np.load(labels_file)\n    def __len__(self):\n        return len(self.features)\n    def __getitem__(self, idx):\n        # feature = self.features[idx]\n        # label = self.labels[idx]\n        feature = torch.from_numpy(self.features[idx]).float()", "detail": "TransCode.utils.dataset", "documentation": {}}, {"label": "save_best_model", "kind": 2, "importPath": "TransCode.utils.helpers", "description": "TransCode.utils.helpers", "peekOfCode": "def save_best_model(model, params, metrics, model_path, global_model_path, results_folder, logger=None):\n    # Save the best performing model and its parameters to disk.\n    os.makedirs(os.path.dirname(model_path), exist_ok=True)\n    try:\n        torch.save(model.state_dict(), model_path)\n        if logger:\n            logger.info(f\"Current best model saved to: {model_path}\")\n    except Exception as e:\n        if logger:\n            logger.error(f\"Error saving current best model: {e}\")", "detail": "TransCode.utils.helpers", "documentation": {}}, {"label": "setup_logger", "kind": 2, "importPath": "TransCode.utils.logger", "description": "TransCode.utils.logger", "peekOfCode": "def setup_logger(log_file=None):\n    logger = logging.getLogger(\"ExperimentLogger\")\n    logger.setLevel(logging.INFO)\n    if not logger.hasHandlers():\n        console_handler = logging.StreamHandler()\n        console_handler.setFormatter(logging.Formatter(\"%(asctime)s - %(levelname)s - %(message)s\"))\n        logger.addHandler(console_handler)\n        if log_file:\n            file_handler = logging.FileHandler(log_file)\n            file_handler.setFormatter(logging.Formatter(\"%(asctime)s - %(levelname)s - %(message)s\"))", "detail": "TransCode.utils.logger", "documentation": {}}, {"label": "calculate_metrics", "kind": 2, "importPath": "TransCode.utils.metrics", "description": "TransCode.utils.metrics", "peekOfCode": "def calculate_metrics(predictions, true_labels):\n    r2 = r2_score(true_labels, predictions)\n    rmse = np.sqrt(mean_squared_error(true_labels, predictions))\n    mae = mean_absolute_error(true_labels, predictions)\n    pcc, _ = pearsonr(true_labels, predictions)\n    scc, _ = spearmanr(true_labels, predictions)\n    return {\"R²\": r2, \"RMSE\": rmse, \"MAE\": mae, \"PCC\": pcc, \"SCC\": scc}", "detail": "TransCode.utils.metrics", "documentation": {}}, {"label": "create_results_folder", "kind": 2, "importPath": "TransCode.utils.save_results", "description": "TransCode.utils.save_results", "peekOfCode": "def create_results_folder(base_dir):\n    # Create a timestamped results folder to store experiment outputs.\n    # timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n    timestamp = f\"{datetime.now().strftime('%Y%m%d_%H%M%S')}\"\n    results_folder = os.path.join(base_dir, timestamp)\n    os.makedirs(results_folder, exist_ok=True)\n    return results_folder\ndef save_metrics_and_results(all_results, best_model_metrics, results_folder, logger=None):\n    # Save all experiment results and best model metrics to JSON files.\n    metrics_file = os.path.join(results_folder, \"metrics.json\")", "detail": "TransCode.utils.save_results", "documentation": {}}, {"label": "save_metrics_and_results", "kind": 2, "importPath": "TransCode.utils.save_results", "description": "TransCode.utils.save_results", "peekOfCode": "def save_metrics_and_results(all_results, best_model_metrics, results_folder, logger=None):\n    # Save all experiment results and best model metrics to JSON files.\n    metrics_file = os.path.join(results_folder, \"metrics.json\")\n    try:\n        with open(metrics_file, \"w\") as f:\n            json.dump(all_results, f, indent=4, ensure_ascii=False)\n        if logger:\n            logger.info(f\"All model evaluation results saved to: {metrics_file}\")\n    except Exception as e:\n        if logger:", "detail": "TransCode.utils.save_results", "documentation": {}}, {"label": "plot_and_save", "kind": 2, "importPath": "TransCode.utils.save_results", "description": "TransCode.utils.save_results", "peekOfCode": "def plot_and_save(predictions, true_labels, results_folder, logger=None):\n    # Generate and save scatter plot of true vs predicted labels and residuals distribution plot.\n    scatter_plot_path = os.path.join(results_folder, \"scatter_plot.png\")\n    residuals_plot_path = os.path.join(results_folder, \"residuals_plot.png\")\n    # Create scatter plot (True vs Predicted values)\n    plt.scatter(true_labels, predictions, alpha=0.6)\n    plt.plot([min(true_labels), max(true_labels)], [min(true_labels), max(true_labels)], color='red', linestyle='--')\n    plt.xlabel(\"True Labels\")\n    plt.ylabel(\"Predicted Labels\")\n    plt.title(\"True vs Predicted Labels\")", "detail": "TransCode.utils.save_results", "documentation": {}}, {"label": "TransformerRegressor", "kind": 6, "importPath": "TransCode.utils.transformer_regressor", "description": "TransCode.utils.transformer_regressor", "peekOfCode": "class TransformerRegressor(nn.Module):\n    def __init__(self, input_dim, d_model=64, nhead=4, num_layers=2, dim_feedforward=128, dropout=0.1):\n        super().__init__()\n        self.embedding = nn.Sequential(\n            nn.Linear(input_dim, d_model),\n            nn.<PERSON>er<PERSON>orm(d_model),\n            nn.ReLU()\n        )\n        encoder_layer = nn.TransformerEncoderLayer(d_model=d_model, nhead=nhead,\n                                                   dim_feedforward=dim_feedforward, dropout=dropout,", "detail": "TransCode.utils.transformer_regressor", "documentation": {}}, {"label": "train_transformer", "kind": 2, "importPath": "TransCode.07training", "description": "TransCode.07training", "peekOfCode": "def train_transformer(model, dataloader, criterion, optimizer, device):\n    model.train()\n    total_loss = 0\n    for inputs, targets in dataloader:\n        inputs = inputs.to(device)\n        targets = targets.to(device)\n        optimizer.zero_grad(set_to_none=True)\n        with torch.cuda.amp.autocast(enabled=False):\n            outputs = model(inputs)\n            loss = criterion(outputs.squeeze(), targets)", "detail": "TransCode.07training", "documentation": {}}, {"label": "evaluate_transformer", "kind": 2, "importPath": "TransCode.07training", "description": "TransCode.07training", "peekOfCode": "def evaluate_transformer(model, dataloader, device):\n    model.eval()\n    predictions, true_labels = [], []\n    with torch.no_grad():\n        for inputs, targets in dataloader:\n            inputs = inputs.to(device).float()\n            outputs = model(inputs)\n            output_np = outputs.squeeze().cpu().numpy()\n            if output_np.ndim == 0:\n                predictions.append(output_np.item())", "detail": "TransCode.07training", "documentation": {}}, {"label": "set_seed", "kind": 2, "importPath": "TransCode.07training", "description": "TransCode.07training", "peekOfCode": "def set_seed(seed=42):\n    torch.manual_seed(seed)\n    np.random.seed(seed)\n    random.seed(seed)\n    torch.cuda.manual_seed_all(seed)\n    torch.backends.cudnn.deterministic = True\n    torch.backends.cudnn.benchmark = False\nif __name__ == \"__main__\":\n    set_seed(42)\n    with open(\"config_complete.json\") as f:", "detail": "TransCode.07training", "documentation": {}}, {"label": "NPYDataset", "kind": 6, "importPath": "kcat_training.utils.dataset", "description": "kcat_training.utils.dataset", "peekOfCode": "class NPYDataset(Dataset):\n    def __init__(self, features_file, labels_file):\n        self.features = np.load(features_file)\n        self.labels = np.load(labels_file)\n    def __len__(self):\n        return len(self.features)\n    def __getitem__(self, idx):\n        # feature = self.features[idx]\n        # label = self.labels[idx]\n        feature = torch.from_numpy(self.features[idx]).float()", "detail": "kcat_training.utils.dataset", "documentation": {}}, {"label": "save_best_model", "kind": 2, "importPath": "kcat_training.utils.helpers", "description": "kcat_training.utils.helpers", "peekOfCode": "def save_best_model(model, params, metrics, model_path, global_model_path, results_folder, logger=None):\n    # Save the best performing model and its parameters to disk.\n    os.makedirs(os.path.dirname(model_path), exist_ok=True)\n    try:\n        torch.save(model.state_dict(), model_path)\n        if logger:\n            logger.info(f\"Current best model saved to: {model_path}\")\n    except Exception as e:\n        if logger:\n            logger.error(f\"Error saving current best model: {e}\")", "detail": "kcat_training.utils.helpers", "documentation": {}}, {"label": "setup_logger", "kind": 2, "importPath": "kcat_training.utils.logger", "description": "kcat_training.utils.logger", "peekOfCode": "def setup_logger(log_file=None):\n    logger = logging.getLogger(\"ExperimentLogger\")\n    logger.setLevel(logging.INFO)\n    if not logger.hasHandlers():\n        console_handler = logging.StreamHandler()\n        console_handler.setFormatter(logging.Formatter(\"%(asctime)s - %(levelname)s - %(message)s\"))\n        logger.addHandler(console_handler)\n        if log_file:\n            file_handler = logging.FileHandler(log_file)\n            file_handler.setFormatter(logging.Formatter(\"%(asctime)s - %(levelname)s - %(message)s\"))", "detail": "kcat_training.utils.logger", "documentation": {}}, {"label": "calculate_metrics", "kind": 2, "importPath": "kcat_training.utils.metrics", "description": "kcat_training.utils.metrics", "peekOfCode": "def calculate_metrics(predictions, true_labels):\n    r2 = r2_score(true_labels, predictions)\n    rmse = np.sqrt(mean_squared_error(true_labels, predictions))\n    mae = mean_absolute_error(true_labels, predictions)\n    pcc, _ = pearsonr(true_labels, predictions)\n    scc, _ = spearmanr(true_labels, predictions)\n    return {\"R²\": r2, \"RMSE\": rmse, \"MAE\": mae, \"PCC\": pcc, \"SCC\": scc}", "detail": "kcat_training.utils.metrics", "documentation": {}}, {"label": "create_results_folder", "kind": 2, "importPath": "kcat_training.utils.save_results", "description": "kcat_training.utils.save_results", "peekOfCode": "def create_results_folder(base_dir):\n    # Create a timestamped results folder to store experiment outputs.\n    # timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n    timestamp = f\"{datetime.now().strftime('%Y%m%d_%H%M%S')}\"\n    results_folder = os.path.join(base_dir, timestamp)\n    os.makedirs(results_folder, exist_ok=True)\n    return results_folder\ndef save_metrics_and_results(all_results, best_model_metrics, results_folder, logger=None):\n    # Save all experiment results and best model metrics to JSON files.\n    metrics_file = os.path.join(results_folder, \"metrics.json\")", "detail": "kcat_training.utils.save_results", "documentation": {}}, {"label": "save_metrics_and_results", "kind": 2, "importPath": "kcat_training.utils.save_results", "description": "kcat_training.utils.save_results", "peekOfCode": "def save_metrics_and_results(all_results, best_model_metrics, results_folder, logger=None):\n    # Save all experiment results and best model metrics to JSON files.\n    metrics_file = os.path.join(results_folder, \"metrics.json\")\n    try:\n        with open(metrics_file, \"w\") as f:\n            json.dump(all_results, f, indent=4, ensure_ascii=False)\n        if logger:\n            logger.info(f\"All model evaluation results saved to: {metrics_file}\")\n    except Exception as e:\n        if logger:", "detail": "kcat_training.utils.save_results", "documentation": {}}, {"label": "plot_and_save", "kind": 2, "importPath": "kcat_training.utils.save_results", "description": "kcat_training.utils.save_results", "peekOfCode": "def plot_and_save(predictions, true_labels, results_folder, logger=None):\n    # Generate and save scatter plot of true vs predicted labels and residuals distribution plot.\n    scatter_plot_path = os.path.join(results_folder, \"scatter_plot.png\")\n    residuals_plot_path = os.path.join(results_folder, \"residuals_plot.png\")\n    # Create scatter plot (True vs Predicted values)\n    plt.scatter(true_labels, predictions, alpha=0.6)\n    plt.plot([min(true_labels), max(true_labels)], [min(true_labels), max(true_labels)], color='red', linestyle='--')\n    plt.xlabel(\"True Labels\")\n    plt.ylabel(\"Predicted Labels\")\n    plt.title(\"True vs Predicted Labels\")", "detail": "kcat_training.utils.save_results", "documentation": {}}, {"label": "TransformerRegressor", "kind": 6, "importPath": "kcat_training.utils.transformer_regressor", "description": "kcat_training.utils.transformer_regressor", "peekOfCode": "class TransformerRegressor(nn.Module):\n    def __init__(self, input_dim, d_model=64, nhead=4, num_layers=2, dim_feedforward=128, dropout=0.1):\n        super().__init__()\n        self.embedding = nn.Sequential(\n            nn.Linear(input_dim, d_model),\n            nn.<PERSON>er<PERSON>orm(d_model),\n            nn.ReLU()\n        )\n        encoder_layer = nn.TransformerEncoderLayer(d_model=d_model, nhead=nhead,\n                                                   dim_feedforward=dim_feedforward, dropout=dropout,", "detail": "kcat_training.utils.transformer_regressor", "documentation": {}}, {"label": "train_transformer", "kind": 2, "importPath": "kcat_training.07training", "description": "kcat_training.07training", "peekOfCode": "def train_transformer(model, dataloader, criterion, optimizer, device):\n    model.train()\n    total_loss = 0\n    for inputs, targets in dataloader:\n        inputs = inputs.to(device)\n        targets = targets.to(device)\n        optimizer.zero_grad(set_to_none=True)\n        with torch.cuda.amp.autocast(enabled=False):\n            outputs = model(inputs)\n            loss = criterion(outputs.squeeze(), targets)", "detail": "kcat_training.07training", "documentation": {}}, {"label": "evaluate_transformer", "kind": 2, "importPath": "kcat_training.07training", "description": "kcat_training.07training", "peekOfCode": "def evaluate_transformer(model, dataloader, device):\n    model.eval()\n    predictions, true_labels = [], []\n    with torch.no_grad():\n        for inputs, targets in dataloader:\n            inputs = inputs.to(device).float()\n            outputs = model(inputs)\n            output_np = outputs.squeeze().cpu().numpy()\n            if output_np.ndim == 0:\n                predictions.append(output_np.item())", "detail": "kcat_training.07training", "documentation": {}}, {"label": "set_seed", "kind": 2, "importPath": "kcat_training.07training", "description": "kcat_training.07training", "peekOfCode": "def set_seed(seed=42):\n    torch.manual_seed(seed)\n    np.random.seed(seed)\n    random.seed(seed)\n    torch.cuda.manual_seed_all(seed)\n    torch.backends.cudnn.deterministic = True\n    torch.backends.cudnn.benchmark = False\nif __name__ == \"__main__\":\n    set_seed(42)\n    with open(\"config_complete.json\") as f:", "detail": "kcat_training.07training", "documentation": {}}, {"label": "analyze_data_distribution", "kind": 2, "importPath": "advanced_data_preprocessing", "description": "advanced_data_preprocessing", "peekOfCode": "def analyze_data_distribution(features, labels, save_dir=\"Result/analysis\"):\n    \"\"\"分析数据分布\"\"\"\n    os.makedirs(save_dir, exist_ok=True)\n    print(\"=== 数据分布分析 ===\")\n    # 标签分布\n    plt.figure(figsize=(12, 4))\n    plt.subplot(1, 3, 1)\n    plt.hist(labels, bins=50, alpha=0.7, edgecolor='black')\n    plt.title('Log10_Kcat_Value Distribution')\n    plt.xlabel('Log10_Kcat_Value')", "detail": "advanced_data_preprocessing", "documentation": {}}, {"label": "remove_outliers", "kind": 2, "importPath": "advanced_data_preprocessing", "description": "advanced_data_preprocessing", "peekOfCode": "def remove_outliers(features, labels, method='iqr', factor=1.5):\n    \"\"\"移除异常值\"\"\"\n    print(f\"\\n=== 异常值处理 ({method}) ===\")\n    original_size = len(labels)\n    if method == 'iqr':\n        Q1 = np.percentile(labels, 25)\n        Q3 = np.percentile(labels, 75)\n        IQR = Q3 - Q1\n        lower_bound = Q1 - factor * IQR\n        upper_bound = Q3 + factor * IQR", "detail": "advanced_data_preprocessing", "documentation": {}}, {"label": "feature_engineering", "kind": 2, "importPath": "advanced_data_preprocessing", "description": "advanced_data_preprocessing", "peekOfCode": "def feature_engineering(features):\n    \"\"\"特征工程\"\"\"\n    print(\"\\n=== 特征工程 ===\")\n    # 1. 移除低方差特征\n    variance_selector = VarianceThreshold(threshold=0.01)\n    features_var_filtered = variance_selector.fit_transform(features)\n    removed_features = features.shape[1] - features_var_filtered.shape[1]\n    print(f\"移除低方差特征: {removed_features}\")\n    # 2. 特征标准化\n    scaler = RobustScaler()  # 对异常值更鲁棒", "detail": "advanced_data_preprocessing", "documentation": {}}, {"label": "advanced_preprocessing", "kind": 2, "importPath": "advanced_data_preprocessing", "description": "advanced_data_preprocessing", "peekOfCode": "def advanced_preprocessing(file_path='kcat_features.tsv', test_size=0.2, random_state=42):\n    \"\"\"高级数据预处理主函数\"\"\"\n    print(\"开始高级数据预处理...\")\n    # 1. 加载原始数据\n    print(\"加载原始数据...\")\n    features = np.load('Result/train_features.npy')\n    labels = np.load('Result/train_labels.npy')\n    test_features = np.load('Result/test_features.npy')\n    test_labels = np.load('Result/test_labels.npy')\n    # 合并训练和测试数据进行统一预处理", "detail": "advanced_data_preprocessing", "documentation": {}}, {"label": "ResidualBlock", "kind": 6, "importPath": "advanced_models", "description": "advanced_models", "peekOfCode": "class ResidualBlock(nn.Module):\n    \"\"\"残差块\"\"\"\n    def __init__(self, dim, dropout=0.1):\n        super().__init__()\n        self.linear1 = nn.Linear(dim, dim)\n        self.linear2 = nn.Linear(dim, dim)\n        self.norm1 = nn.LayerNorm(dim)\n        self.norm2 = nn.LayerNorm(dim)\n        self.dropout = nn.Dropout(dropout)\n        self.activation = nn.GELU()", "detail": "advanced_models", "documentation": {}}, {"label": "AttentionBlock", "kind": 6, "importPath": "advanced_models", "description": "advanced_models", "peekOfCode": "class AttentionBlock(nn.Module):\n    \"\"\"自注意力块\"\"\"\n    def __init__(self, dim, num_heads=8, dropout=0.1):\n        super().__init__()\n        self.attention = nn.MultiheadAttention(dim, num_heads, dropout=dropout, batch_first=True)\n        self.norm = nn.LayerNorm(dim)\n        self.dropout = nn.Dropout(dropout)\n    def forward(self, x):\n        # x shape: (batch_size, seq_len, dim)\n        residual = x", "detail": "advanced_models", "documentation": {}}, {"label": "ImprovedTransformerRegressor", "kind": 6, "importPath": "advanced_models", "description": "advanced_models", "peekOfCode": "class ImprovedTransformerRegressor(nn.Module):\n    \"\"\"改进的Transformer回归器\"\"\"\n    def __init__(self, input_dim, d_model=256, nhead=8, num_layers=6, \n                 dim_feedforward=1024, dropout=0.1, num_residual_blocks=3):\n        super().__init__()\n        # 输入嵌入层\n        self.input_projection = nn.Sequential(\n            nn.Linear(input_dim, d_model),\n            nn.LayerNorm(d_model),\n            nn.GELU(),", "detail": "advanced_models", "documentation": {}}, {"label": "DeepMLPRegressor", "kind": 6, "importPath": "advanced_models", "description": "advanced_models", "peekOfCode": "class DeepMLPRegressor(nn.Module):\n    \"\"\"深度MLP回归器\"\"\"\n    def __init__(self, input_dim, hidden_dims=[512, 256, 128, 64], dropout=0.2):\n        super().__init__()\n        layers = []\n        prev_dim = input_dim\n        for hidden_dim in hidden_dims:\n            layers.extend([\n                nn.Linear(prev_dim, hidden_dim),\n                nn.BatchNorm1d(hidden_dim),", "detail": "advanced_models", "documentation": {}}, {"label": "HybridRegressor", "kind": 6, "importPath": "advanced_models", "description": "advanced_models", "peekOfCode": "class HybridRegressor(nn.Module):\n    \"\"\"混合架构回归器（CNN + Transformer + MLP）\"\"\"\n    def __init__(self, input_dim, d_model=256, nhead=8, num_layers=4, dropout=0.1):\n        super().__init__()\n        # 1D CNN分支\n        self.cnn_branch = nn.Sequential(\n            nn.Conv1d(1, 64, kernel_size=3, padding=1),\n            nn.BatchNorm1d(64),\n            nn.GELU(),\n            nn.Conv1d(64, 128, kernel_size=3, padding=1),", "detail": "advanced_models", "documentation": {}}, {"label": "EnsembleRegressor", "kind": 6, "importPath": "advanced_models", "description": "advanced_models", "peekOfCode": "class EnsembleRegressor(nn.Module):\n    \"\"\"集成回归器\"\"\"\n    def __init__(self, models, weights=None):\n        super().__init__()\n        self.models = nn.ModuleList(models)\n        if weights is None:\n            weights = [1.0 / len(models)] * len(models)\n        self.register_buffer('weights', torch.tensor(weights))\n    def forward(self, x):\n        outputs = []", "detail": "advanced_models", "documentation": {}}, {"label": "create_model", "kind": 2, "importPath": "advanced_models", "description": "advanced_models", "peekOfCode": "def create_model(model_type, input_dim, **kwargs):\n    \"\"\"创建模型的工厂函数\"\"\"\n    if model_type == 'improved_transformer':\n        return ImprovedTransformerRegressor(input_dim, **kwargs)\n    elif model_type == 'deep_mlp':\n        return DeepMLPRegressor(input_dim, **kwargs)\n    elif model_type == 'hybrid':\n        return HybridRegressor(input_dim, **kwargs)\n    else:\n        raise ValueError(f\"Unknown model type: {model_type}\")", "detail": "advanced_models", "documentation": {}}, {"label": "EarlyStopping", "kind": 6, "importPath": "advanced_training", "description": "advanced_training", "peekOfCode": "class EarlyStopping:\n    \"\"\"早停机制\"\"\"\n    def __init__(self, patience=20, min_delta=0.001, restore_best_weights=True):\n        self.patience = patience\n        self.min_delta = min_delta\n        self.restore_best_weights = restore_best_weights\n        self.best_loss = float('inf')\n        self.counter = 0\n        self.best_weights = None\n    def __call__(self, val_loss, model):", "detail": "advanced_training", "documentation": {}}, {"label": "<PERSON><PERSON>L<PERSON>", "kind": 6, "importPath": "advanced_training", "description": "advanced_training", "peekOfCode": "class HuberLoss(nn.Module):\n    \"\"\"Huber损失函数，对异常值更鲁棒\"\"\"\n    def __init__(self, delta=1.0):\n        super().__init__()\n        self.delta = delta\n    def forward(self, pred, target):\n        residual = torch.abs(pred - target)\n        condition = residual < self.delta\n        squared_loss = 0.5 * residual ** 2\n        linear_loss = self.delta * residual - 0.5 * self.delta ** 2", "detail": "advanced_training", "documentation": {}}, {"label": "CombinedLoss", "kind": 6, "importPath": "advanced_training", "description": "advanced_training", "peekOfCode": "class CombinedLoss(nn.Module):\n    \"\"\"组合损失函数\"\"\"\n    def __init__(self, mse_weight=0.7, mae_weight=0.2, huber_weight=0.1):\n        super().__init__()\n        self.mse_weight = mse_weight\n        self.mae_weight = mae_weight\n        self.huber_weight = huber_weight\n        self.mse_loss = nn.MSELoss()\n        self.mae_loss = nn.L1Loss()\n        self.huber_loss = HuberLoss(delta=1.0)", "detail": "advanced_training", "documentation": {}}, {"label": "calculate_metrics", "kind": 2, "importPath": "advanced_training", "description": "advanced_training", "peekOfCode": "def calculate_metrics(predictions, true_labels):\n    \"\"\"计算评估指标\"\"\"\n    r2 = r2_score(true_labels, predictions)\n    rmse = np.sqrt(mean_squared_error(true_labels, predictions))\n    mae = mean_absolute_error(true_labels, predictions)\n    pcc, _ = pearsonr(true_labels, predictions)\n    scc, _ = spearmanr(true_labels, predictions)\n    return {\"R²\": r2, \"RMSE\": rmse, \"MAE\": mae, \"PCC\": pcc, \"SCC\": scc}\ndef train_single_model(model, train_loader, val_loader, config, device):\n    \"\"\"训练单个模型\"\"\"", "detail": "advanced_training", "documentation": {}}, {"label": "train_single_model", "kind": 2, "importPath": "advanced_training", "description": "advanced_training", "peekOfCode": "def train_single_model(model, train_loader, val_loader, config, device):\n    \"\"\"训练单个模型\"\"\"\n    model = model.to(device)\n    # 优化器\n    optimizer = optim.AdamW(\n        model.parameters(), \n        lr=config['learning_rate'],\n        weight_decay=config.get('weight_decay', 0.01),\n        betas=(0.9, 0.999),\n        eps=1e-8", "detail": "advanced_training", "documentation": {}}, {"label": "advanced_training", "kind": 2, "importPath": "advanced_training", "description": "advanced_training", "peekOfCode": "def advanced_training():\n    \"\"\"高级训练主函数\"\"\"\n    print(\"开始高级模型训练...\")\n    # 设备设置\n    device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n    print(f\"使用设备: {device}\")\n    if torch.cuda.is_available():\n        print(f\"GPU: {torch.cuda.get_device_name(0)}\")\n    # 加载预处理后的数据\n    X_train = np.load('Result/advanced/train_features.npy')", "detail": "advanced_training", "documentation": {}}, {"label": "parse_vector_string", "kind": 2, "importPath": "data_preprocessing", "description": "data_preprocessing", "peekOfCode": "def parse_vector_string(vector_str):\n    \"\"\"解析向量字符串为numpy数组\"\"\"\n    try:\n        # 移除引号并解析为浮点数列表\n        vector_str = vector_str.strip('\"')\n        vector_list = [float(x) for x in vector_str.split(',')]\n        return np.array(vector_list)\n    except Exception as e:\n        print(f\"Error parsing vector: {e}\")\n        return None", "detail": "data_preprocessing", "documentation": {}}, {"label": "load_and_preprocess_data", "kind": 2, "importPath": "data_preprocessing", "description": "data_preprocessing", "peekOfCode": "def load_and_preprocess_data(file_path):\n    \"\"\"加载和预处理数据\"\"\"\n    print(\"Loading data...\")\n    # 读取TSV文件，处理引号问题\n    try:\n        df = pd.read_csv(file_path, sep='\\t', quoting=3, on_bad_lines='skip')\n    except:\n        # 如果上面的方法失败，尝试其他参数\n        try:\n            df = pd.read_csv(file_path, sep='\\t', quotechar='\"', doublequote=True, on_bad_lines='skip')", "detail": "data_preprocessing", "documentation": {}}, {"label": "split_and_save_data", "kind": 2, "importPath": "data_preprocessing", "description": "data_preprocessing", "peekOfCode": "def split_and_save_data(features, labels, test_size=0.2, random_state=42):\n    \"\"\"划分训练测试集并保存\"\"\"\n    print(\"Splitting data...\")\n    X_train, X_test, y_train, y_test = train_test_split(\n        features, labels, test_size=test_size, random_state=random_state\n    )\n    print(f\"Training set: {X_train.shape[0]} samples\")\n    print(f\"Test set: {X_test.shape[0]} samples\")\n    # 创建结果目录\n    os.makedirs('Result', exist_ok=True)", "detail": "data_preprocessing", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "data_preprocessing", "description": "data_preprocessing", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    try:\n        # 加载和预处理数据\n        features, labels = load_and_preprocess_data('kcat_features.tsv')\n        # 划分和保存数据\n        X_train, X_test, y_train, y_test = split_and_save_data(features, labels)\n        print(\"\\nData preprocessing completed successfully!\")\n        print(f\"Training features shape: {X_train.shape}\")\n        print(f\"Training labels shape: {y_train.shape}\")", "detail": "data_preprocessing", "documentation": {}}, {"label": "feature_selection_analysis", "kind": 2, "importPath": "extreme_optimization", "description": "extreme_optimization", "peekOfCode": "def feature_selection_analysis(X_train, y_train, X_test, y_test):\n    \"\"\"特征选择分析\"\"\"\n    print(\"=== 特征选择分析 ===\")\n    # 1. 基于F统计量的特征选择\n    selector_f = SelectKBest(score_func=f_regression, k=200)\n    X_train_f = selector_f.fit_transform(X_train, y_train)\n    X_test_f = selector_f.transform(X_test)\n    # 2. 基于互信息的特征选择\n    selector_mi = SelectKBest(score_func=mutual_info_regression, k=200)\n    X_train_mi = selector_mi.fit_transform(X_train, y_train)", "detail": "extreme_optimization", "documentation": {}}, {"label": "polynomial_features_experiment", "kind": 2, "importPath": "extreme_optimization", "description": "extreme_optimization", "peekOfCode": "def polynomial_features_experiment(X_train, y_train, X_test, y_test):\n    \"\"\"多项式特征实验\"\"\"\n    print(\"\\n=== 多项式特征实验 ===\")\n    # 只对前50个最重要的特征生成多项式特征，避免维度爆炸\n    rf = RandomForestRegressor(n_estimators=100, random_state=42)\n    rf.fit(X_train, y_train)\n    top_50_features = np.argsort(rf.feature_importances_)[-50:]\n    X_train_top = X_train[:, top_50_features]\n    X_test_top = X_test[:, top_50_features]\n    # 生成2次多项式特征", "detail": "extreme_optimization", "documentation": {}}, {"label": "ensemble_methods_experiment", "kind": 2, "importPath": "extreme_optimization", "description": "extreme_optimization", "peekOfCode": "def ensemble_methods_experiment(X_train, y_train, X_test, y_test):\n    \"\"\"集成方法实验\"\"\"\n    print(\"\\n=== 集成方法实验 ===\")\n    models = {\n        'XGBoost': xgb.XGBRegressor(\n            n_estimators=500,\n            max_depth=8,\n            learning_rate=0.05,\n            subsample=0.8,\n            colsample_bytree=0.8,", "detail": "extreme_optimization", "documentation": {}}, {"label": "stacking_experiment", "kind": 2, "importPath": "extreme_optimization", "description": "extreme_optimization", "peekOfCode": "def stacking_experiment(X_train, y_train, X_test, y_test):\n    \"\"\"堆叠集成实验\"\"\"\n    print(\"\\n=== 堆叠集成实验 ===\")\n    from sklearn.model_selection import KFold\n    from sklearn.linear_model import LinearRegression\n    # 基学习器\n    base_models = {\n        'xgb': xgb.XGBRegressor(n_estimators=300, max_depth=6, learning_rate=0.05, random_state=42),\n        'lgb': lgb.LGBMRegressor(n_estimators=300, max_depth=6, learning_rate=0.05, random_state=42, verbose=-1),\n        'rf': RandomForestRegressor(n_estimators=300, max_depth=10, random_state=42, n_jobs=-1)", "detail": "extreme_optimization", "documentation": {}}, {"label": "extreme_optimization_pipeline", "kind": 2, "importPath": "extreme_optimization", "description": "extreme_optimization", "peekOfCode": "def extreme_optimization_pipeline():\n    \"\"\"极端优化流水线\"\"\"\n    print(\"开始极端优化实验...\")\n    # 加载数据\n    X_train = np.load('Result/advanced/train_features.npy')\n    y_train = np.load('Result/advanced/train_labels.npy')\n    X_test = np.load('Result/advanced/test_features.npy')\n    y_test = np.load('Result/advanced/test_labels.npy')\n    print(f\"原始数据: 训练集 {X_train.shape}, 测试集 {X_test.shape}\")\n    results = {}", "detail": "extreme_optimization", "documentation": {}}, {"label": "TabNet", "kind": 6, "importPath": "final_neural_optimization", "description": "final_neural_optimization", "peekOfCode": "class TabNet(nn.Module):\n    \"\"\"TabNet架构 - 专门为表格数据设计的神经网络\"\"\"\n    def __init__(self, input_dim, output_dim=1, n_d=64, n_a=64, n_steps=5, gamma=1.3, n_independent=2, n_shared=2, epsilon=1e-15):\n        super().__init__()\n        self.input_dim = input_dim\n        self.output_dim = output_dim\n        self.n_d = n_d\n        self.n_a = n_a\n        self.n_steps = n_steps\n        self.gamma = gamma", "detail": "final_neural_optimization", "documentation": {}}, {"label": "ResNetRegressor", "kind": 6, "importPath": "final_neural_optimization", "description": "final_neural_optimization", "peekOfCode": "class ResNetRegressor(nn.Module):\n    \"\"\"ResNet风格的回归器\"\"\"\n    def __init__(self, input_dim, hidden_dims=[512, 256, 128], dropout=0.3):\n        super().__init__()\n        self.input_layer = nn.Sequential(\n            nn.Linear(input_dim, hidden_dims[0]),\n            nn.BatchNorm1d(hidden_dims[0]),\n            nn.ReLU(),\n            nn.Dropout(dropout)\n        )", "detail": "final_neural_optimization", "documentation": {}}, {"label": "WideAndDeepRegressor", "kind": 6, "importPath": "final_neural_optimization", "description": "final_neural_optimization", "peekOfCode": "class WideAndDeepRegressor(nn.Module):\n    \"\"\"Wide & Deep架构\"\"\"\n    def __init__(self, input_dim, deep_dims=[512, 256, 128], dropout=0.2):\n        super().__init__()\n        # Wide部分（线性）\n        self.wide = nn.Linear(input_dim, 1)\n        # Deep部分\n        deep_layers = []\n        prev_dim = input_dim\n        for dim in deep_dims:", "detail": "final_neural_optimization", "documentation": {}}, {"label": "train_advanced_neural_network", "kind": 2, "importPath": "final_neural_optimization", "description": "final_neural_optimization", "peekOfCode": "def train_advanced_neural_network(model, train_loader, val_loader, epochs=200, device='cuda'):\n    \"\"\"训练高级神经网络\"\"\"\n    model = model.to(device)\n    # 使用OneCycleLR学习率调度\n    optimizer = optim.AdamW(model.parameters(), lr=0.01, weight_decay=0.01)\n    scheduler = OneCycleLR(optimizer, max_lr=0.01, epochs=epochs, steps_per_epoch=len(train_loader))\n    # 使用SmoothL1Loss（Huber Loss）\n    criterion = nn.SmoothL1Loss()\n    best_val_r2 = -float('inf')\n    patience = 30", "detail": "final_neural_optimization", "documentation": {}}, {"label": "final_neural_optimization", "kind": 2, "importPath": "final_neural_optimization", "description": "final_neural_optimization", "peekOfCode": "def final_neural_optimization():\n    \"\"\"最终神经网络优化\"\"\"\n    print(\"开始最终神经网络优化...\")\n    device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n    print(f\"使用设备: {device}\")\n    # 加载数据\n    X_train = np.load('Result/advanced/train_features.npy')\n    y_train = np.load('Result/advanced/train_labels.npy')\n    X_test = np.load('Result/advanced/test_features.npy')\n    y_test = np.load('Result/advanced/test_labels.npy')", "detail": "final_neural_optimization", "documentation": {}}, {"label": "load_best_model", "kind": 2, "importPath": "generate_best_model_plots", "description": "generate_best_model_plots", "peekOfCode": "def load_best_model():\n    \"\"\"加载R²最好的模型\"\"\"\n    print(\"使用R²最好的原始Transformer模型...\")\n    # 直接使用我们知道R²最好的原始模型\n    best_model_path = 'Result/Transformer_Model/20250629_230536/best_model.pt'\n    if not os.path.exists(best_model_path):\n        raise ValueError(f\"最佳模型文件不存在: {best_model_path}\")\n    try:\n        checkpoint = torch.load(best_model_path, map_location='cpu', weights_only=False)\n        print(f\"成功加载模型: {best_model_path}\")", "detail": "generate_best_model_plots", "documentation": {}}, {"label": "create_model_from_checkpoint", "kind": 2, "importPath": "generate_best_model_plots", "description": "generate_best_model_plots", "peekOfCode": "def create_model_from_checkpoint(checkpoint, input_dim):\n    \"\"\"根据checkpoint创建模型\"\"\"\n    # 根据保存的权重推断正确的参数\n    model = TransformerRegressor(\n        input_dim=input_dim,\n        d_model=64,\n        nhead=4,\n        num_layers=2,\n        dim_feedforward=256,  # 从错误信息看应该是256\n        dropout=0.1", "detail": "generate_best_model_plots", "documentation": {}}, {"label": "generate_predictions_and_plots", "kind": 2, "importPath": "generate_best_model_plots", "description": "generate_best_model_plots", "peekOfCode": "def generate_predictions_and_plots():\n    \"\"\"生成预测并绘制散点图\"\"\"\n    print(\"开始生成预测和散点图...\")\n    device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n    print(f\"使用设备: {device}\")\n    # 1. 加载最佳模型\n    best_model_path, best_model_info, best_r2 = load_best_model()\n    # 2. 加载数据\n    # 首先尝试加载原始数据\n    try:", "detail": "generate_best_model_plots", "documentation": {}}, {"label": "show_results", "kind": 2, "importPath": "show_plot_results", "description": "show_plot_results", "peekOfCode": "def show_results():\n    \"\"\"显示散点图生成结果\"\"\"\n    results_dir = \"Result/Best_Model_Plots/20250630_105402\"\n    print(\"🎯 R²最佳模型散点图生成结果\")\n    print(\"=\" * 50)\n    # 读取模型信息\n    info_file = os.path.join(results_dir, \"model_info.json\")\n    if os.path.exists(info_file):\n        with open(info_file, 'r') as f:\n            model_info = json.load(f)", "detail": "show_plot_results", "documentation": {}}, {"label": "DeepResidualRegressor", "kind": 6, "importPath": "simplified_final_optimization", "description": "simplified_final_optimization", "peekOfCode": "class DeepResidualRegressor(nn.Module):\n    \"\"\"深度残差回归器\"\"\"\n    def __init__(self, input_dim, hidden_dims=[1024, 512, 256, 128, 64], dropout=0.3):\n        super().__init__()\n        # 输入层\n        self.input_layer = nn.Sequential(\n            nn.Linear(input_dim, hidden_dims[0]),\n            nn.BatchNorm1d(hidden_dims[0]),\n            nn.GELU(),\n            nn.Dropout(dropout)", "detail": "simplified_final_optimization", "documentation": {}}, {"label": "AttentionRegressor", "kind": 6, "importPath": "simplified_final_optimization", "description": "simplified_final_optimization", "peekOfCode": "class AttentionRegressor(nn.Module):\n    \"\"\"基于注意力的回归器\"\"\"\n    def __init__(self, input_dim, d_model=512, nhead=16, num_layers=8, dropout=0.2):\n        super().__init__()\n        # 输入投影\n        self.input_projection = nn.Sequential(\n            nn.Linear(input_dim, d_model),\n            nn.LayerNorm(d_model),\n            nn.GELU(),\n            nn.Dropout(dropout)", "detail": "simplified_final_optimization", "documentation": {}}, {"label": "WideDeepRegressor", "kind": 6, "importPath": "simplified_final_optimization", "description": "simplified_final_optimization", "peekOfCode": "class WideDeepRegressor(nn.Module):\n    \"\"\"Wide & Deep模型\"\"\"\n    def __init__(self, input_dim, deep_dims=[1024, 512, 256, 128], dropout=0.2):\n        super().__init__()\n        # Wide部分（线性模型）\n        self.wide = nn.Linear(input_dim, 1)\n        # Deep部分\n        deep_layers = []\n        prev_dim = input_dim\n        for dim in deep_dims:", "detail": "simplified_final_optimization", "documentation": {}}, {"label": "train_model_with_advanced_techniques", "kind": 2, "importPath": "simplified_final_optimization", "description": "simplified_final_optimization", "peekOfCode": "def train_model_with_advanced_techniques(model, train_loader, val_loader, epochs=300, device='cuda'):\n    \"\"\"使用高级技术训练模型\"\"\"\n    model = model.to(device)\n    # 优化器和调度器\n    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.01, betas=(0.9, 0.999))\n    scheduler = CosineAnnealingLR(optimizer, T_max=epochs, eta_min=1e-6)\n    # 损失函数（组合损失）\n    mse_loss = nn.MSELoss()\n    mae_loss = nn.L1Loss()\n    def combined_loss(pred, target):", "detail": "simplified_final_optimization", "documentation": {}}, {"label": "final_optimization", "kind": 2, "importPath": "simplified_final_optimization", "description": "simplified_final_optimization", "peekOfCode": "def final_optimization():\n    \"\"\"最终优化主函数\"\"\"\n    print(\"开始最终神经网络优化...\")\n    device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n    print(f\"使用设备: {device}\")\n    if torch.cuda.is_available():\n        print(f\"GPU: {torch.cuda.get_device_name(0)}\")\n    # 加载数据\n    X_train = np.load('Result/advanced/train_features.npy')\n    y_train = np.load('Result/advanced/train_labels.npy')", "detail": "simplified_final_optimization", "documentation": {}}]