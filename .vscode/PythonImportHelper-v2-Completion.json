[{"label": "numpy", "kind": 6, "isExtraImport": true, "importPath": "numpy", "description": "numpy", "detail": "numpy", "documentation": {}}, {"label": "torch", "kind": 6, "isExtraImport": true, "importPath": "torch", "description": "torch", "detail": "torch", "documentation": {}}, {"label": "Dataset", "importPath": "torch.utils.data", "description": "torch.utils.data", "isExtraImport": true, "detail": "torch.utils.data", "documentation": {}}, {"label": "DataLoader", "importPath": "torch.utils.data", "description": "torch.utils.data", "isExtraImport": true, "detail": "torch.utils.data", "documentation": {}}, {"label": "TensorDataset", "importPath": "torch.utils.data", "description": "torch.utils.data", "isExtraImport": true, "detail": "torch.utils.data", "documentation": {}}, {"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "json", "kind": 6, "isExtraImport": true, "importPath": "json", "description": "json", "detail": "json", "documentation": {}}, {"label": "pickle", "kind": 6, "isExtraImport": true, "importPath": "pickle", "description": "pickle", "detail": "pickle", "documentation": {}}, {"label": "shutil", "kind": 6, "isExtraImport": true, "importPath": "shutil", "description": "shutil", "detail": "shutil", "documentation": {}}, {"label": "logging", "kind": 6, "isExtraImport": true, "importPath": "logging", "description": "logging", "detail": "logging", "documentation": {}}, {"label": "r2_score", "importPath": "sklearn.metrics", "description": "sklearn.metrics", "isExtraImport": true, "detail": "sklearn.metrics", "documentation": {}}, {"label": "mean_squared_error", "importPath": "sklearn.metrics", "description": "sklearn.metrics", "isExtraImport": true, "detail": "sklearn.metrics", "documentation": {}}, {"label": "mean_absolute_error", "importPath": "sklearn.metrics", "description": "sklearn.metrics", "isExtraImport": true, "detail": "sklearn.metrics", "documentation": {}}, {"label": "pear<PERSON><PERSON>", "importPath": "scipy.stats", "description": "scipy.stats", "isExtraImport": true, "detail": "scipy.stats", "documentation": {}}, {"label": "spearmanr", "importPath": "scipy.stats", "description": "scipy.stats", "isExtraImport": true, "detail": "scipy.stats", "documentation": {}}, {"label": "matplotlib.pyplot", "kind": 6, "isExtraImport": true, "importPath": "matplotlib.pyplot", "description": "matplotlib.pyplot", "detail": "matplotlib.pyplot", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "torch.nn", "kind": 6, "isExtraImport": true, "importPath": "torch.nn", "description": "torch.nn", "detail": "torch.nn", "documentation": {}}, {"label": "pandas", "kind": 6, "isExtraImport": true, "importPath": "pandas", "description": "pandas", "detail": "pandas", "documentation": {}}, {"label": "train_test_split", "importPath": "sklearn.model_selection", "description": "sklearn.model_selection", "isExtraImport": true, "detail": "sklearn.model_selection", "documentation": {}}, {"label": "ast", "kind": 6, "isExtraImport": true, "importPath": "ast", "description": "ast", "detail": "ast", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "plot_and_save", "importPath": "utils.save_results", "description": "utils.save_results", "isExtraImport": true, "detail": "utils.save_results", "documentation": {}}, {"label": "create_results_folder", "importPath": "utils.save_results", "description": "utils.save_results", "isExtraImport": true, "detail": "utils.save_results", "documentation": {}}, {"label": "TransformerRegressor", "importPath": "utils.transformer_regressor", "description": "utils.transformer_regressor", "isExtraImport": true, "detail": "utils.transformer_regressor", "documentation": {}}, {"label": "calculate_metrics", "importPath": "utils.metrics", "description": "utils.metrics", "isExtraImport": true, "detail": "utils.metrics", "documentation": {}}, {"label": "NPYDataset", "kind": 6, "importPath": "TransCode.utils.dataset", "description": "TransCode.utils.dataset", "peekOfCode": "class NPYDataset(Dataset):\n    def __init__(self, features_file, labels_file):\n        self.features = np.load(features_file)\n        self.labels = np.load(labels_file)\n    def __len__(self):\n        return len(self.features)\n    def __getitem__(self, idx):\n        # feature = self.features[idx]\n        # label = self.labels[idx]\n        feature = torch.from_numpy(self.features[idx]).float()", "detail": "TransCode.utils.dataset", "documentation": {}}, {"label": "save_best_model", "kind": 2, "importPath": "TransCode.utils.helpers", "description": "TransCode.utils.helpers", "peekOfCode": "def save_best_model(model, params, metrics, model_path, global_model_path, results_folder, logger=None):\n    # Save the best performing model and its parameters to disk.\n    os.makedirs(os.path.dirname(model_path), exist_ok=True)\n    try:\n        torch.save(model.state_dict(), model_path)\n        if logger:\n            logger.info(f\"Current best model saved to: {model_path}\")\n    except Exception as e:\n        if logger:\n            logger.error(f\"Error saving current best model: {e}\")", "detail": "TransCode.utils.helpers", "documentation": {}}, {"label": "setup_logger", "kind": 2, "importPath": "TransCode.utils.logger", "description": "TransCode.utils.logger", "peekOfCode": "def setup_logger(log_file=None):\n    logger = logging.getLogger(\"ExperimentLogger\")\n    logger.setLevel(logging.INFO)\n    if not logger.hasHandlers():\n        console_handler = logging.StreamHandler()\n        console_handler.setFormatter(logging.Formatter(\"%(asctime)s - %(levelname)s - %(message)s\"))\n        logger.addHandler(console_handler)\n        if log_file:\n            file_handler = logging.FileHandler(log_file)\n            file_handler.setFormatter(logging.Formatter(\"%(asctime)s - %(levelname)s - %(message)s\"))", "detail": "TransCode.utils.logger", "documentation": {}}, {"label": "calculate_metrics", "kind": 2, "importPath": "TransCode.utils.metrics", "description": "TransCode.utils.metrics", "peekOfCode": "def calculate_metrics(predictions, true_labels):\n    r2 = r2_score(true_labels, predictions)\n    rmse = np.sqrt(mean_squared_error(true_labels, predictions))\n    mae = mean_absolute_error(true_labels, predictions)\n    pcc, _ = pearsonr(true_labels, predictions)\n    scc, _ = spearmanr(true_labels, predictions)\n    return {\"R²\": r2, \"RMSE\": rmse, \"MAE\": mae, \"PCC\": pcc, \"SCC\": scc}", "detail": "TransCode.utils.metrics", "documentation": {}}, {"label": "create_results_folder", "kind": 2, "importPath": "TransCode.utils.save_results", "description": "TransCode.utils.save_results", "peekOfCode": "def create_results_folder(base_dir):\n    # Create a timestamped results folder to store experiment outputs.\n    # timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n    timestamp = f\"{datetime.now().strftime('%Y%m%d_%H%M%S')}\"\n    results_folder = os.path.join(base_dir, timestamp)\n    os.makedirs(results_folder, exist_ok=True)\n    return results_folder\ndef save_metrics_and_results(all_results, best_model_metrics, results_folder, logger=None):\n    # Save all experiment results and best model metrics to JSON files.\n    metrics_file = os.path.join(results_folder, \"metrics.json\")", "detail": "TransCode.utils.save_results", "documentation": {}}, {"label": "save_metrics_and_results", "kind": 2, "importPath": "TransCode.utils.save_results", "description": "TransCode.utils.save_results", "peekOfCode": "def save_metrics_and_results(all_results, best_model_metrics, results_folder, logger=None):\n    # Save all experiment results and best model metrics to JSON files.\n    metrics_file = os.path.join(results_folder, \"metrics.json\")\n    try:\n        with open(metrics_file, \"w\") as f:\n            json.dump(all_results, f, indent=4, ensure_ascii=False)\n        if logger:\n            logger.info(f\"All model evaluation results saved to: {metrics_file}\")\n    except Exception as e:\n        if logger:", "detail": "TransCode.utils.save_results", "documentation": {}}, {"label": "plot_and_save", "kind": 2, "importPath": "TransCode.utils.save_results", "description": "TransCode.utils.save_results", "peekOfCode": "def plot_and_save(predictions, true_labels, results_folder, logger=None):\n    # Generate and save scatter plot of true vs predicted labels and residuals distribution plot.\n    scatter_plot_path = os.path.join(results_folder, \"scatter_plot.png\")\n    residuals_plot_path = os.path.join(results_folder, \"residuals_plot.png\")\n    # Create scatter plot (True vs Predicted values)\n    plt.scatter(true_labels, predictions, alpha=0.6)\n    plt.plot([min(true_labels), max(true_labels)], [min(true_labels), max(true_labels)], color='red', linestyle='--')\n    plt.xlabel(\"True Labels\")\n    plt.ylabel(\"Predicted Labels\")\n    plt.title(\"True vs Predicted Labels\")", "detail": "TransCode.utils.save_results", "documentation": {}}, {"label": "TransformerRegressor", "kind": 6, "importPath": "TransCode.utils.transformer_regressor", "description": "TransCode.utils.transformer_regressor", "peekOfCode": "class TransformerRegressor(nn.Module):\n    def __init__(self, input_dim, d_model=64, nhead=4, num_layers=2, dim_feedforward=128, dropout=0.1):\n        super().__init__()\n        self.embedding = nn.Sequential(\n            nn.Linear(input_dim, d_model),\n            nn.<PERSON>er<PERSON>orm(d_model),\n            nn.ReLU()\n        )\n        encoder_layer = nn.TransformerEncoderLayer(d_model=d_model, nhead=nhead,\n                                                   dim_feedforward=dim_feedforward, dropout=dropout,", "detail": "TransCode.utils.transformer_regressor", "documentation": {}}, {"label": "parse_vector_string", "kind": 2, "importPath": "data_preprocessing", "description": "data_preprocessing", "peekOfCode": "def parse_vector_string(vector_str):\n    \"\"\"解析向量字符串为numpy数组\"\"\"\n    try:\n        # 移除引号并解析为浮点数列表\n        vector_str = vector_str.strip('\"')\n        vector_list = [float(x) for x in vector_str.split(',')]\n        return np.array(vector_list)\n    except Exception as e:\n        print(f\"Error parsing vector: {e}\")\n        return None", "detail": "data_preprocessing", "documentation": {}}, {"label": "load_and_preprocess_data", "kind": 2, "importPath": "data_preprocessing", "description": "data_preprocessing", "peekOfCode": "def load_and_preprocess_data(file_path):\n    \"\"\"加载和预处理数据\"\"\"\n    print(\"Loading data...\")\n    # 读取TSV文件，处理引号问题\n    try:\n        df = pd.read_csv(file_path, sep='\\t', quoting=3, on_bad_lines='skip')\n    except:\n        # 如果上面的方法失败，尝试其他参数\n        try:\n            df = pd.read_csv(file_path, sep='\\t', quotechar='\"', doublequote=True, on_bad_lines='skip')", "detail": "data_preprocessing", "documentation": {}}, {"label": "split_and_save_data", "kind": 2, "importPath": "data_preprocessing", "description": "data_preprocessing", "peekOfCode": "def split_and_save_data(features, labels, test_size=0.2, random_state=42):\n    \"\"\"划分训练测试集并保存\"\"\"\n    print(\"Splitting data...\")\n    X_train, X_test, y_train, y_test = train_test_split(\n        features, labels, test_size=test_size, random_state=random_state\n    )\n    print(f\"Training set: {X_train.shape[0]} samples\")\n    print(f\"Test set: {X_test.shape[0]} samples\")\n    # 创建结果目录\n    os.makedirs('Result', exist_ok=True)", "detail": "data_preprocessing", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "data_preprocessing", "description": "data_preprocessing", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    try:\n        # 加载和预处理数据\n        features, labels = load_and_preprocess_data('kcat_features.tsv')\n        # 划分和保存数据\n        X_train, X_test, y_train, y_test = split_and_save_data(features, labels)\n        print(\"\\nData preprocessing completed successfully!\")\n        print(f\"Training features shape: {X_train.shape}\")\n        print(f\"Training labels shape: {y_train.shape}\")", "detail": "data_preprocessing", "documentation": {}}, {"label": "load_best_model", "kind": 2, "importPath": "generate_best_model_plots", "description": "generate_best_model_plots", "peekOfCode": "def load_best_model():\n    \"\"\"加载R²最好的模型\"\"\"\n    print(\"使用R²最好的原始Transformer模型...\")\n    # 直接使用我们知道R²最好的原始模型\n    best_model_path = 'Result/Transformer_Model/20250629_230536/best_model.pt'\n    if not os.path.exists(best_model_path):\n        raise ValueError(f\"最佳模型文件不存在: {best_model_path}\")\n    try:\n        checkpoint = torch.load(best_model_path, map_location='cpu', weights_only=False)\n        print(f\"成功加载模型: {best_model_path}\")", "detail": "generate_best_model_plots", "documentation": {}}, {"label": "create_model_from_checkpoint", "kind": 2, "importPath": "generate_best_model_plots", "description": "generate_best_model_plots", "peekOfCode": "def create_model_from_checkpoint(checkpoint, input_dim):\n    \"\"\"根据checkpoint创建模型\"\"\"\n    # 根据保存的权重推断正确的参数\n    model = TransformerRegressor(\n        input_dim=input_dim,\n        d_model=64,\n        nhead=4,\n        num_layers=2,\n        dim_feedforward=256,  # 从错误信息看应该是256\n        dropout=0.1", "detail": "generate_best_model_plots", "documentation": {}}, {"label": "generate_predictions_and_plots", "kind": 2, "importPath": "generate_best_model_plots", "description": "generate_best_model_plots", "peekOfCode": "def generate_predictions_and_plots():\n    \"\"\"生成预测并绘制散点图\"\"\"\n    print(\"开始生成预测和散点图...\")\n    device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n    print(f\"使用设备: {device}\")\n    # 1. 加载最佳模型\n    best_model_path, best_model_info, best_r2 = load_best_model()\n    # 2. 加载数据\n    # 首先尝试加载原始数据\n    try:", "detail": "generate_best_model_plots", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "run_demo", "description": "run_demo", "peekOfCode": "def main():\n    \"\"\"主演示函数\"\"\"\n    print(\"🧬 Kcat预测模型演示\")\n    print(\"=\" * 50)\n    print(\"\\n📋 可用操作:\")\n    print(\"1. 查看模型性能和可视化结果\")\n    print(\"2. 重新生成散点图和残差图\")\n    print(\"3. 查看项目结构\")\n    print(\"4. 退出\")\n    while True:", "detail": "run_demo", "documentation": {}}, {"label": "print_project_structure", "kind": 2, "importPath": "run_demo", "description": "run_demo", "peekOfCode": "def print_project_structure():\n    \"\"\"打印项目结构\"\"\"\n    structure = \"\"\"\n📁 项目结构:\nkcat/\n├── 📊 kcat_features.tsv                    # 原始数据文件 (393MB)\n├── 🔧 data_preprocessing.py                # 数据预处理脚本\n├── 📈 generate_best_model_plots.py         # 生成散点图脚本\n├── 👁️  show_plot_results.py                 # 显示结果脚本\n├── 🚀 run_demo.py                          # 演示脚本 (当前)", "detail": "run_demo", "documentation": {}}, {"label": "show_results", "kind": 2, "importPath": "show_plot_results", "description": "show_plot_results", "peekOfCode": "def show_results():\n    \"\"\"显示散点图生成结果\"\"\"\n    results_dir = \"Result/Best_Model_Plots/20250630_105402\"\n    print(\"🎯 R²最佳模型散点图生成结果\")\n    print(\"=\" * 50)\n    # 读取模型信息\n    info_file = os.path.join(results_dir, \"model_info.json\")\n    if os.path.exists(info_file):\n        with open(info_file, 'r') as f:\n            model_info = json.load(f)", "detail": "show_plot_results", "documentation": {}}]