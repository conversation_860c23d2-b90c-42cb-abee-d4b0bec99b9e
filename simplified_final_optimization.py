#!/usr/bin/env python3
"""
简化的最终优化策略
使用可靠的神经网络架构和最佳实践
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from scipy.stats import pearsonr
import matplotlib.pyplot as plt
from torch.optim.lr_scheduler import OneCycleLR, CosineAnnealingLR
import torch.nn.functional as F

class DeepResidualRegressor(nn.Module):
    """深度残差回归器"""
    def __init__(self, input_dim, hidden_dims=[1024, 512, 256, 128, 64], dropout=0.3):
        super().__init__()
        
        # 输入层
        self.input_layer = nn.Sequential(
            nn.Linear(input_dim, hidden_dims[0]),
            nn.BatchNorm1d(hidden_dims[0]),
            nn.GELU(),
            nn.Dropout(dropout)
        )
        
        # 残差块
        self.residual_blocks = nn.ModuleList()
        for i in range(len(hidden_dims) - 1):
            self.residual_blocks.append(
                self._make_residual_block(hidden_dims[i], hidden_dims[i+1], dropout)
            )
        
        # 输出层
        self.output_layer = nn.Sequential(
            nn.Linear(hidden_dims[-1], hidden_dims[-1] // 2),
            nn.BatchNorm1d(hidden_dims[-1] // 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dims[-1] // 2, 1)
        )
        
        # 权重初始化
        self.apply(self._init_weights)
    
    def _make_residual_block(self, in_dim, out_dim, dropout):
        return nn.Sequential(
            nn.Linear(in_dim, out_dim),
            nn.BatchNorm1d(out_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(out_dim, out_dim),
            nn.BatchNorm1d(out_dim)
        )
    
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            torch.nn.init.kaiming_normal_(module.weight, mode='fan_out', nonlinearity='relu')
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.BatchNorm1d):
            torch.nn.init.ones_(module.weight)
            torch.nn.init.zeros_(module.bias)
    
    def forward(self, x):
        x = self.input_layer(x)
        
        for i, block in enumerate(self.residual_blocks):
            residual = x
            x = block(x)
            
            # 如果维度匹配，添加残差连接
            if x.size(1) == residual.size(1):
                x = x + residual
            
            x = F.gelu(x)
        
        return self.output_layer(x).squeeze(-1)

class AttentionRegressor(nn.Module):
    """基于注意力的回归器"""
    def __init__(self, input_dim, d_model=512, nhead=16, num_layers=8, dropout=0.2):
        super().__init__()
        
        # 输入投影
        self.input_projection = nn.Sequential(
            nn.Linear(input_dim, d_model),
            nn.LayerNorm(d_model),
            nn.GELU(),
            nn.Dropout(dropout)
        )
        
        # 位置编码
        self.pos_encoding = nn.Parameter(torch.randn(1, 1, d_model))
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation='gelu',
            batch_first=True,
            norm_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool1d(1)
        
        # 输出层
        self.output_layer = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.LayerNorm(d_model // 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, d_model // 4),
            nn.LayerNorm(d_model // 4),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 4, 1)
        )
        
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.ones_(module.weight)
            torch.nn.init.zeros_(module.bias)
    
    def forward(self, x):
        # 输入投影
        x = self.input_projection(x)  # (batch_size, d_model)
        
        # 添加序列维度和位置编码
        x = x.unsqueeze(1) + self.pos_encoding  # (batch_size, 1, d_model)
        
        # Transformer编码
        x = self.transformer(x)  # (batch_size, 1, d_model)
        
        # 全局池化
        x = x.squeeze(1)  # (batch_size, d_model)
        
        # 输出预测
        return self.output_layer(x).squeeze(-1)

class WideDeepRegressor(nn.Module):
    """Wide & Deep模型"""
    def __init__(self, input_dim, deep_dims=[1024, 512, 256, 128], dropout=0.2):
        super().__init__()
        
        # Wide部分（线性模型）
        self.wide = nn.Linear(input_dim, 1)
        
        # Deep部分
        deep_layers = []
        prev_dim = input_dim
        for dim in deep_dims:
            deep_layers.extend([
                nn.Linear(prev_dim, dim),
                nn.BatchNorm1d(dim),
                nn.GELU(),
                nn.Dropout(dropout)
            ])
            prev_dim = dim
        
        self.deep = nn.Sequential(*deep_layers)
        self.deep_output = nn.Linear(deep_dims[-1], 1)
        
        # 组合层
        self.combine = nn.Sequential(
            nn.Linear(2, 32),
            nn.GELU(),
            nn.Linear(32, 1)
        )
        
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            torch.nn.init.kaiming_normal_(module.weight)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
    
    def forward(self, x):
        wide_out = self.wide(x)
        deep_out = self.deep_output(self.deep(x))
        
        combined = torch.cat([wide_out, deep_out], dim=1)
        return self.combine(combined).squeeze(-1)

def train_model_with_advanced_techniques(model, train_loader, val_loader, epochs=300, device='cuda'):
    """使用高级技术训练模型"""
    model = model.to(device)
    
    # 优化器和调度器
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.01, betas=(0.9, 0.999))
    scheduler = CosineAnnealingLR(optimizer, T_max=epochs, eta_min=1e-6)
    
    # 损失函数（组合损失）
    mse_loss = nn.MSELoss()
    mae_loss = nn.L1Loss()
    
    def combined_loss(pred, target):
        return 0.7 * mse_loss(pred, target) + 0.3 * mae_loss(pred, target)
    
    best_val_r2 = -float('inf')
    patience = 50
    patience_counter = 0
    best_model_state = None
    
    train_losses = []
    val_r2_scores = []
    
    print(f"开始训练，总共 {epochs} 轮...")
    
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        epoch_train_loss = 0
        
        for batch_features, batch_labels in train_loader:
            batch_features, batch_labels = batch_features.to(device), batch_labels.to(device)
            
            optimizer.zero_grad()
            outputs = model(batch_features)
            loss = combined_loss(outputs, batch_labels)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            epoch_train_loss += loss.item()
        
        scheduler.step()
        
        # 验证阶段
        model.eval()
        val_predictions = []
        val_targets = []
        
        with torch.no_grad():
            for batch_features, batch_labels in val_loader:
                batch_features, batch_labels = batch_features.to(device), batch_labels.to(device)
                outputs = model(batch_features)
                val_predictions.extend(outputs.cpu().numpy())
                val_targets.extend(batch_labels.cpu().numpy())
        
        val_r2 = r2_score(val_targets, val_predictions)
        
        train_losses.append(epoch_train_loss / len(train_loader))
        val_r2_scores.append(val_r2)
        
        # 早停和最佳模型保存
        if val_r2 > best_val_r2:
            best_val_r2 = val_r2
            patience_counter = 0
            best_model_state = model.state_dict().copy()
        else:
            patience_counter += 1
        
        # 打印进度
        if (epoch + 1) % 25 == 0:
            current_lr = optimizer.param_groups[0]['lr']
            print(f"Epoch {epoch+1}/{epochs}: Train Loss: {train_losses[-1]:.4f}, "
                  f"Val R²: {val_r2:.4f}, Best R²: {best_val_r2:.4f}, LR: {current_lr:.2e}")
        
        # 早停检查
        if patience_counter >= patience:
            print(f"Early stopping at epoch {epoch+1}")
            break
    
    # 加载最佳模型
    if best_model_state is not None:
        model.load_state_dict(best_model_state)
    
    return model, best_val_r2, train_losses, val_r2_scores

def final_optimization():
    """最终优化主函数"""
    print("开始最终神经网络优化...")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name(0)}")
    
    # 加载数据
    X_train = np.load('Result/advanced/train_features.npy')
    y_train = np.load('Result/advanced/train_labels.npy')
    X_test = np.load('Result/advanced/test_features.npy')
    y_test = np.load('Result/advanced/test_labels.npy')
    
    print(f"数据形状: 训练集 {X_train.shape}, 测试集 {X_test.shape}")
    
    # 创建数据加载器
    train_dataset = TensorDataset(torch.FloatTensor(X_train), torch.FloatTensor(y_train))
    test_dataset = TensorDataset(torch.FloatTensor(X_test), torch.FloatTensor(y_test))
    
    train_loader = DataLoader(train_dataset, batch_size=256, shuffle=True, num_workers=4)
    test_loader = DataLoader(test_dataset, batch_size=256, shuffle=False, num_workers=4)
    
    input_dim = X_train.shape[1]
    
    # 定义模型
    models = {
        'DeepResidual': DeepResidualRegressor(input_dim, hidden_dims=[1024, 512, 256, 128, 64]),
        'Attention': AttentionRegressor(input_dim, d_model=512, nhead=16, num_layers=8),
        'WideDeep': WideDeepRegressor(input_dim, deep_dims=[1024, 512, 256, 128])
    }
    
    results = {}
    trained_models = {}
    
    # 训练每个模型
    for name, model in models.items():
        print(f"\n{'='*60}")
        print(f"训练 {name} 模型")
        print(f"{'='*60}")
        
        trained_model, best_r2, train_losses, val_r2_scores = train_model_with_advanced_techniques(
            model, train_loader, test_loader, epochs=400, device=device
        )
        
        # 最终测试评估
        trained_model.eval()
        test_predictions = []
        test_targets = []
        
        with torch.no_grad():
            for batch_features, batch_labels in test_loader:
                batch_features = batch_features.to(device)
                outputs = trained_model(batch_features)
                test_predictions.extend(outputs.cpu().numpy())
                test_targets.extend(batch_labels.numpy())
        
        # 计算详细指标
        final_r2 = r2_score(test_targets, test_predictions)
        rmse = np.sqrt(mean_squared_error(test_targets, test_predictions))
        mae = mean_absolute_error(test_targets, test_predictions)
        pcc, _ = pearsonr(test_predictions, test_targets)
        
        results[name] = {
            'R²': final_r2,
            'RMSE': rmse,
            'MAE': mae,
            'PCC': pcc
        }
        
        trained_models[name] = trained_model
        
        print(f"{name} 最终结果:")
        print(f"  R²: {final_r2:.4f}")
        print(f"  RMSE: {rmse:.4f}")
        print(f"  MAE: {mae:.4f}")
        print(f"  PCC: {pcc:.4f}")
        
        # 保存模型
        torch.save({
            'model_state_dict': trained_model.state_dict(),
            'model_type': name,
            'metrics': results[name],
            'train_losses': train_losses,
            'val_r2_scores': val_r2_scores
        }, f'Result/final_{name.lower()}_optimized.pt')
    
    # 集成预测
    print(f"\n{'='*60}")
    print("集成模型预测")
    print(f"{'='*60}")
    
    ensemble_predictions = []
    weights = []
    
    for name, model in trained_models.items():
        model.eval()
        predictions = []
        with torch.no_grad():
            for batch_features, batch_labels in test_loader:
                batch_features = batch_features.to(device)
                outputs = model(batch_features)
                predictions.extend(outputs.cpu().numpy())
        
        ensemble_predictions.append(predictions)
        weights.append(results[name]['R²'])  # 基于R²的权重
    
    # 加权平均集成
    weights = np.array(weights)
    weights = weights / weights.sum()
    
    weighted_ensemble = np.average(ensemble_predictions, axis=0, weights=weights)
    ensemble_r2 = r2_score(test_targets, weighted_ensemble)
    ensemble_rmse = np.sqrt(mean_squared_error(test_targets, weighted_ensemble))
    ensemble_pcc, _ = pearsonr(weighted_ensemble, test_targets)
    
    results['WeightedEnsemble'] = {
        'R²': ensemble_r2,
        'RMSE': ensemble_rmse,
        'MAE': mean_absolute_error(test_targets, weighted_ensemble),
        'PCC': ensemble_pcc
    }
    
    print(f"加权集成结果:")
    print(f"  R²: {ensemble_r2:.4f}")
    print(f"  RMSE: {ensemble_rmse:.4f}")
    print(f"  PCC: {ensemble_pcc:.4f}")
    
    # 最终结果汇总
    print(f"\n{'='*60}")
    print("最终结果汇总")
    print(f"{'='*60}")
    
    for name, metrics in results.items():
        print(f"{name:15s}: R² = {metrics['R²']:.4f}, RMSE = {metrics['RMSE']:.4f}, PCC = {metrics['PCC']:.4f}")
    
    best_method = max(results, key=lambda x: results[x]['R²'])
    best_r2 = results[best_method]['R²']
    
    print(f"\n最佳方法: {best_method}")
    print(f"最佳R²: {best_r2:.4f}")
    
    if best_r2 >= 0.6:
        print("🎉 成功达到R² >= 0.6的目标！")
    else:
        print(f"⚠️  距离R² = 0.6还差 {0.6 - best_r2:.4f}")
        print("\n可能的改进方向:")
        print("1. 收集更多高质量的训练数据")
        print("2. 引入更多的分子和蛋白质特征")
        print("3. 使用更先进的分子表示方法（如图神经网络）")
        print("4. 考虑实验条件等额外因素")
        print("5. 数据质量检查和清洗")
    
    return results, best_method, best_r2

if __name__ == "__main__":
    results, best_method, best_r2 = final_optimization()
