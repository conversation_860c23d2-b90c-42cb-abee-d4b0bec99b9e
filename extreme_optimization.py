#!/usr/bin/env python3
"""
极端优化策略
尝试多种激进的方法来提升R²到0.6以上
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression
from sklearn.decomposition import PCA, FastICA
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import ElasticNet, Ridge
from sklearn.preprocessing import PolynomialFeatures
from sklearn.metrics import r2_score
from sklearn.model_selection import cross_val_score
import xgboost as xgb
import lightgbm as lgb
from scipy.stats import pearsonr
import warnings
warnings.filterwarnings('ignore')

def feature_selection_analysis(X_train, y_train, X_test, y_test):
    """特征选择分析"""
    print("=== 特征选择分析 ===")
    
    # 1. 基于F统计量的特征选择
    selector_f = SelectKBest(score_func=f_regression, k=200)
    X_train_f = selector_f.fit_transform(X_train, y_train)
    X_test_f = selector_f.transform(X_test)
    
    # 2. 基于互信息的特征选择
    selector_mi = SelectKBest(score_func=mutual_info_regression, k=200)
    X_train_mi = selector_mi.fit_transform(X_train, y_train)
    X_test_mi = selector_mi.transform(X_test)
    
    # 3. 基于随机森林重要性的特征选择
    rf = RandomForestRegressor(n_estimators=100, random_state=42)
    rf.fit(X_train, y_train)
    feature_importance = rf.feature_importances_
    top_features = np.argsort(feature_importance)[-200:]
    X_train_rf = X_train[:, top_features]
    X_test_rf = X_test[:, top_features]
    
    # 测试不同特征选择方法
    methods = [
        ("Original", X_train, X_test),
        ("F-statistic", X_train_f, X_test_f),
        ("Mutual Info", X_train_mi, X_test_mi),
        ("RF Importance", X_train_rf, X_test_rf)
    ]
    
    results = {}
    for name, X_tr, X_te in methods:
        # 使用随机森林测试
        rf_test = RandomForestRegressor(n_estimators=200, random_state=42, n_jobs=-1)
        rf_test.fit(X_tr, y_train)
        y_pred = rf_test.predict(X_te)
        r2 = r2_score(y_test, y_pred)
        results[name] = r2
        print(f"{name}: R² = {r2:.4f}")
    
    # 返回最佳特征选择方法
    best_method = max(results, key=results.get)
    print(f"最佳特征选择方法: {best_method}")
    
    if best_method == "F-statistic":
        return X_train_f, X_test_f, selector_f
    elif best_method == "Mutual Info":
        return X_train_mi, X_test_mi, selector_mi
    elif best_method == "RF Importance":
        return X_train_rf, X_test_rf, top_features
    else:
        return X_train, X_test, None

def polynomial_features_experiment(X_train, y_train, X_test, y_test):
    """多项式特征实验"""
    print("\n=== 多项式特征实验 ===")
    
    # 只对前50个最重要的特征生成多项式特征，避免维度爆炸
    rf = RandomForestRegressor(n_estimators=100, random_state=42)
    rf.fit(X_train, y_train)
    top_50_features = np.argsort(rf.feature_importances_)[-50:]
    
    X_train_top = X_train[:, top_50_features]
    X_test_top = X_test[:, top_50_features]
    
    # 生成2次多项式特征
    poly = PolynomialFeatures(degree=2, interaction_only=True, include_bias=False)
    X_train_poly = poly.fit_transform(X_train_top)
    X_test_poly = poly.transform(X_test_top)
    
    print(f"多项式特征维度: {X_train_poly.shape[1]}")
    
    # 使用ElasticNet处理高维特征
    elastic = ElasticNet(alpha=0.1, l1_ratio=0.5, random_state=42)
    elastic.fit(X_train_poly, y_train)
    y_pred = elastic.predict(X_test_poly)
    r2 = r2_score(y_test, y_pred)
    
    print(f"多项式特征 + ElasticNet R²: {r2:.4f}")
    
    return X_train_poly, X_test_poly, r2

def ensemble_methods_experiment(X_train, y_train, X_test, y_test):
    """集成方法实验"""
    print("\n=== 集成方法实验 ===")
    
    models = {
        'XGBoost': xgb.XGBRegressor(
            n_estimators=500,
            max_depth=8,
            learning_rate=0.05,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            n_jobs=-1
        ),
        'LightGBM': lgb.LGBMRegressor(
            n_estimators=500,
            max_depth=8,
            learning_rate=0.05,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            n_jobs=-1,
            verbose=-1
        ),
        'GradientBoosting': GradientBoostingRegressor(
            n_estimators=500,
            max_depth=8,
            learning_rate=0.05,
            subsample=0.8,
            random_state=42
        ),
        'RandomForest': RandomForestRegressor(
            n_estimators=500,
            max_depth=15,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42,
            n_jobs=-1
        )
    }
    
    results = {}
    trained_models = {}
    
    for name, model in models.items():
        print(f"训练 {name}...")
        model.fit(X_train, y_train)
        y_pred = model.predict(X_test)
        r2 = r2_score(y_test, y_pred)
        results[name] = r2
        trained_models[name] = model
        print(f"{name} R²: {r2:.4f}")
    
    # 集成预测
    predictions = []
    for model in trained_models.values():
        predictions.append(model.predict(X_test))
    
    # 简单平均集成
    ensemble_pred = np.mean(predictions, axis=0)
    ensemble_r2 = r2_score(y_test, ensemble_pred)
    print(f"简单平均集成 R²: {ensemble_r2:.4f}")
    
    # 加权集成（基于性能）
    weights = np.array(list(results.values()))
    weights = weights / weights.sum()
    weighted_pred = np.average(predictions, axis=0, weights=weights)
    weighted_r2 = r2_score(y_test, weighted_pred)
    print(f"加权集成 R²: {weighted_r2:.4f}")
    
    return max(ensemble_r2, weighted_r2), trained_models

def stacking_experiment(X_train, y_train, X_test, y_test):
    """堆叠集成实验"""
    print("\n=== 堆叠集成实验 ===")
    
    from sklearn.model_selection import KFold
    from sklearn.linear_model import LinearRegression
    
    # 基学习器
    base_models = {
        'xgb': xgb.XGBRegressor(n_estimators=300, max_depth=6, learning_rate=0.05, random_state=42),
        'lgb': lgb.LGBMRegressor(n_estimators=300, max_depth=6, learning_rate=0.05, random_state=42, verbose=-1),
        'rf': RandomForestRegressor(n_estimators=300, max_depth=10, random_state=42, n_jobs=-1)
    }
    
    # 生成元特征
    kf = KFold(n_splits=5, shuffle=True, random_state=42)
    meta_features_train = np.zeros((X_train.shape[0], len(base_models)))
    meta_features_test = np.zeros((X_test.shape[0], len(base_models)))
    
    for i, (name, model) in enumerate(base_models.items()):
        print(f"生成 {name} 的元特征...")
        test_preds = []
        
        for train_idx, val_idx in kf.split(X_train):
            X_fold_train, X_fold_val = X_train[train_idx], X_train[val_idx]
            y_fold_train, y_fold_val = y_train[train_idx], y_train[val_idx]
            
            model.fit(X_fold_train, y_fold_train)
            meta_features_train[val_idx, i] = model.predict(X_fold_val)
            test_preds.append(model.predict(X_test))
        
        meta_features_test[:, i] = np.mean(test_preds, axis=0)
    
    # 元学习器
    meta_model = LinearRegression()
    meta_model.fit(meta_features_train, y_train)
    stacking_pred = meta_model.predict(meta_features_test)
    stacking_r2 = r2_score(y_test, stacking_pred)
    
    print(f"堆叠集成 R²: {stacking_r2:.4f}")
    return stacking_r2

def extreme_optimization_pipeline():
    """极端优化流水线"""
    print("开始极端优化实验...")
    
    # 加载数据
    X_train = np.load('Result/advanced/train_features.npy')
    y_train = np.load('Result/advanced/train_labels.npy')
    X_test = np.load('Result/advanced/test_features.npy')
    y_test = np.load('Result/advanced/test_labels.npy')
    
    print(f"原始数据: 训练集 {X_train.shape}, 测试集 {X_test.shape}")
    
    results = {}
    
    # 1. 特征选择实验
    X_train_selected, X_test_selected, selector = feature_selection_analysis(X_train, y_train, X_test, y_test)
    
    # 2. 多项式特征实验
    X_train_poly, X_test_poly, poly_r2 = polynomial_features_experiment(X_train_selected, y_train, X_test_selected, y_test)
    results['Polynomial Features'] = poly_r2
    
    # 3. 集成方法实验
    ensemble_r2, ensemble_models = ensemble_methods_experiment(X_train_selected, y_train, X_test_selected, y_test)
    results['Ensemble Methods'] = ensemble_r2
    
    # 4. 堆叠集成实验
    stacking_r2 = stacking_experiment(X_train_selected, y_train, X_test_selected, y_test)
    results['Stacking'] = stacking_r2
    
    # 5. 尝试原始数据上的最佳方法
    print("\n=== 原始数据上的最佳方法测试 ===")
    best_xgb = xgb.XGBRegressor(
        n_estimators=1000,
        max_depth=10,
        learning_rate=0.03,
        subsample=0.8,
        colsample_bytree=0.8,
        reg_alpha=0.1,
        reg_lambda=0.1,
        random_state=42,
        n_jobs=-1
    )
    best_xgb.fit(X_train, y_train)
    best_pred = best_xgb.predict(X_test)
    best_r2 = r2_score(y_test, best_pred)
    results['Best XGBoost'] = best_r2
    print(f"最佳XGBoost (原始数据) R²: {best_r2:.4f}")
    
    print(f"\n=== 最终结果汇总 ===")
    for method, r2 in results.items():
        print(f"{method}: R² = {r2:.4f}")
    
    best_method = max(results, key=results.get)
    best_r2 = results[best_method]
    print(f"\n最佳方法: {best_method}")
    print(f"最佳R²: {best_r2:.4f}")
    
    if best_r2 >= 0.6:
        print("🎉 成功达到R² >= 0.6的目标！")
    else:
        print(f"⚠️  距离R² = 0.6还差 {0.6 - best_r2:.4f}")
        print("建议：可能需要更多的特征工程或外部数据")
    
    return results, best_method, best_r2

if __name__ == "__main__":
    results, best_method, best_r2 = extreme_optimization_pipeline()
