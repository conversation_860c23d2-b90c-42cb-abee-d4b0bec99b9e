#!/usr/bin/env python3
"""
改进的模型架构
包含更深的网络、残差连接、批归一化、注意力机制等
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math

class ResidualBlock(nn.Module):
    """残差块"""
    def __init__(self, dim, dropout=0.1):
        super().__init__()
        self.linear1 = nn.Linear(dim, dim)
        self.linear2 = nn.Linear(dim, dim)
        self.norm1 = nn.LayerNorm(dim)
        self.norm2 = nn.LayerNorm(dim)
        self.dropout = nn.Dropout(dropout)
        self.activation = nn.GELU()
        
    def forward(self, x):
        residual = x
        x = self.norm1(x)
        x = self.linear1(x)
        x = self.activation(x)
        x = self.dropout(x)
        x = self.linear2(x)
        x = self.dropout(x)
        return x + residual

class AttentionBlock(nn.Module):
    """自注意力块"""
    def __init__(self, dim, num_heads=8, dropout=0.1):
        super().__init__()
        self.attention = nn.MultiheadAttention(dim, num_heads, dropout=dropout, batch_first=True)
        self.norm = nn.LayerNorm(dim)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        # x shape: (batch_size, seq_len, dim)
        residual = x
        x = self.norm(x)
        attn_output, _ = self.attention(x, x, x)
        x = self.dropout(attn_output)
        return x + residual

class ImprovedTransformerRegressor(nn.Module):
    """改进的Transformer回归器"""
    def __init__(self, input_dim, d_model=256, nhead=8, num_layers=6, 
                 dim_feedforward=1024, dropout=0.1, num_residual_blocks=3):
        super().__init__()
        
        # 输入嵌入层
        self.input_projection = nn.Sequential(
            nn.Linear(input_dim, d_model),
            nn.LayerNorm(d_model),
            nn.GELU(),
            nn.Dropout(dropout)
        )
        
        # Transformer编码器层
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model, 
            nhead=nhead,
            dim_feedforward=dim_feedforward, 
            dropout=dropout,
            activation='gelu',
            batch_first=True,
            norm_first=True  # Pre-norm架构
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # 残差块
        self.residual_blocks = nn.ModuleList([
            ResidualBlock(d_model, dropout) for _ in range(num_residual_blocks)
        ])
        
        # 注意力池化
        self.attention_pool = nn.Sequential(
            nn.Linear(d_model, 1),
            nn.Softmax(dim=1)
        )
        
        # 回归头
        self.regressor = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.LayerNorm(d_model // 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, d_model // 4),
            nn.LayerNorm(d_model // 4),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 4, 1)
        )
        
        # 权重初始化
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.ones_(module.weight)
            torch.nn.init.zeros_(module.bias)
    
    def forward(self, x):
        # 输入投影
        x = self.input_projection(x)  # (batch_size, d_model)
        
        # 添加序列维度用于Transformer
        x = x.unsqueeze(1)  # (batch_size, 1, d_model)
        
        # Transformer编码
        x = self.transformer_encoder(x)  # (batch_size, 1, d_model)
        
        # 移除序列维度
        x = x.squeeze(1)  # (batch_size, d_model)
        
        # 残差块
        for residual_block in self.residual_blocks:
            x = residual_block(x)
        
        # 回归预测
        output = self.regressor(x)
        return output.squeeze(-1)

class DeepMLPRegressor(nn.Module):
    """深度MLP回归器"""
    def __init__(self, input_dim, hidden_dims=[512, 256, 128, 64], dropout=0.2):
        super().__init__()
        
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.GELU(),
                nn.Dropout(dropout)
            ])
            prev_dim = hidden_dim
        
        # 输出层
        layers.append(nn.Linear(prev_dim, 1))
        
        self.network = nn.Sequential(*layers)
        
        # 权重初始化
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            torch.nn.init.kaiming_normal_(module.weight, mode='fan_out', nonlinearity='relu')
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.BatchNorm1d):
            torch.nn.init.ones_(module.weight)
            torch.nn.init.zeros_(module.bias)
    
    def forward(self, x):
        return self.network(x).squeeze(-1)

class HybridRegressor(nn.Module):
    """混合架构回归器（CNN + Transformer + MLP）"""
    def __init__(self, input_dim, d_model=256, nhead=8, num_layers=4, dropout=0.1):
        super().__init__()
        
        # 1D CNN分支
        self.cnn_branch = nn.Sequential(
            nn.Conv1d(1, 64, kernel_size=3, padding=1),
            nn.BatchNorm1d(64),
            nn.GELU(),
            nn.Conv1d(64, 128, kernel_size=3, padding=1),
            nn.BatchNorm1d(128),
            nn.GELU(),
            nn.AdaptiveAvgPool1d(d_model // 4)
        )
        
        # Transformer分支
        self.transformer_projection = nn.Linear(input_dim, d_model)
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model, nhead=nhead, dim_feedforward=d_model*2,
            dropout=dropout, activation='gelu', batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # MLP分支
        self.mlp_branch = nn.Sequential(
            nn.Linear(input_dim, d_model),
            nn.LayerNorm(d_model),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.LayerNorm(d_model // 2),
            nn.GELU(),
            nn.Dropout(dropout)
        )
        
        # 融合层
        fusion_dim = 128 * (d_model // 4) + d_model + (d_model // 2)
        self.fusion = nn.Sequential(
            nn.Linear(fusion_dim, d_model),
            nn.LayerNorm(d_model),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.LayerNorm(d_model // 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, 1)
        )
        
    def forward(self, x):
        batch_size = x.size(0)
        
        # CNN分支
        cnn_input = x.unsqueeze(1)  # (batch_size, 1, input_dim)
        cnn_out = self.cnn_branch(cnn_input)  # (batch_size, 128, d_model//4)
        cnn_out = cnn_out.view(batch_size, -1)  # 展平
        
        # Transformer分支
        transformer_input = self.transformer_projection(x).unsqueeze(1)  # (batch_size, 1, d_model)
        transformer_out = self.transformer(transformer_input).squeeze(1)  # (batch_size, d_model)
        
        # MLP分支
        mlp_out = self.mlp_branch(x)  # (batch_size, d_model//2)
        
        # 融合所有分支
        fused = torch.cat([cnn_out, transformer_out, mlp_out], dim=1)
        output = self.fusion(fused)
        
        return output.squeeze(-1)

class EnsembleRegressor(nn.Module):
    """集成回归器"""
    def __init__(self, models, weights=None):
        super().__init__()
        self.models = nn.ModuleList(models)
        if weights is None:
            weights = [1.0 / len(models)] * len(models)
        self.register_buffer('weights', torch.tensor(weights))

    def forward(self, x):
        outputs = []
        for model in self.models:
            outputs.append(model(x))

        # 加权平均
        ensemble_output = sum(w * out for w, out in zip(self.weights, outputs))
        return ensemble_output

def create_model(model_type, input_dim, **kwargs):
    """创建模型的工厂函数"""
    if model_type == 'improved_transformer':
        return ImprovedTransformerRegressor(input_dim, **kwargs)
    elif model_type == 'deep_mlp':
        return DeepMLPRegressor(input_dim, **kwargs)
    elif model_type == 'hybrid':
        return HybridRegressor(input_dim, **kwargs)
    else:
        raise ValueError(f"Unknown model type: {model_type}")
