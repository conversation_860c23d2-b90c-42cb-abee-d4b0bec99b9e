#!/usr/bin/env python3
"""
改进的训练脚本
包含学习率调度、早停、权重衰减、混合损失函数等优化技术
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from torch.optim.lr_scheduler import CosineAnnealingLR, ReduceLROnPlateau
import numpy as np
import json
import os
from datetime import datetime
import matplotlib.pyplot as plt
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from scipy.stats import pearsonr, spearmanr
import warnings
warnings.filterwarnings('ignore')

from advanced_models import create_model, EnsembleRegressor

class EarlyStopping:
    """早停机制"""
    def __init__(self, patience=20, min_delta=0.001, restore_best_weights=True):
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        self.best_loss = float('inf')
        self.counter = 0
        self.best_weights = None
        
    def __call__(self, val_loss, model):
        if val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.counter = 0
            if self.restore_best_weights:
                self.best_weights = model.state_dict().copy()
        else:
            self.counter += 1
            
        if self.counter >= self.patience:
            if self.restore_best_weights and self.best_weights:
                model.load_state_dict(self.best_weights)
            return True
        return False

class HuberLoss(nn.Module):
    """Huber损失函数，对异常值更鲁棒"""
    def __init__(self, delta=1.0):
        super().__init__()
        self.delta = delta
        
    def forward(self, pred, target):
        residual = torch.abs(pred - target)
        condition = residual < self.delta
        squared_loss = 0.5 * residual ** 2
        linear_loss = self.delta * residual - 0.5 * self.delta ** 2
        return torch.where(condition, squared_loss, linear_loss).mean()

class CombinedLoss(nn.Module):
    """组合损失函数"""
    def __init__(self, mse_weight=0.7, mae_weight=0.2, huber_weight=0.1):
        super().__init__()
        self.mse_weight = mse_weight
        self.mae_weight = mae_weight
        self.huber_weight = huber_weight
        self.mse_loss = nn.MSELoss()
        self.mae_loss = nn.L1Loss()
        self.huber_loss = HuberLoss(delta=1.0)
        
    def forward(self, pred, target):
        mse = self.mse_loss(pred, target)
        mae = self.mae_loss(pred, target)
        huber = self.huber_loss(pred, target)
        return self.mse_weight * mse + self.mae_weight * mae + self.huber_weight * huber

def calculate_metrics(predictions, true_labels):
    """计算评估指标"""
    r2 = r2_score(true_labels, predictions)
    rmse = np.sqrt(mean_squared_error(true_labels, predictions))
    mae = mean_absolute_error(true_labels, predictions)
    pcc, _ = pearsonr(true_labels, predictions)
    scc, _ = spearmanr(true_labels, predictions)
    return {"R²": r2, "RMSE": rmse, "MAE": mae, "PCC": pcc, "SCC": scc}

def train_single_model(model, train_loader, val_loader, config, device):
    """训练单个模型"""
    model = model.to(device)
    
    # 优化器
    optimizer = optim.AdamW(
        model.parameters(), 
        lr=config['learning_rate'],
        weight_decay=config.get('weight_decay', 0.01),
        betas=(0.9, 0.999),
        eps=1e-8
    )
    
    # 学习率调度器
    if config.get('scheduler') == 'cosine':
        scheduler = CosineAnnealingLR(optimizer, T_max=config['epochs'], eta_min=1e-6)
    elif config.get('scheduler') == 'plateau':
        scheduler = ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=10, verbose=True)
    else:
        scheduler = None
    
    # 损失函数
    if config.get('loss_type') == 'combined':
        criterion = CombinedLoss()
    elif config.get('loss_type') == 'huber':
        criterion = HuberLoss()
    else:
        criterion = nn.MSELoss()
    
    # 早停
    early_stopping = EarlyStopping(
        patience=config.get('patience', 30),
        min_delta=config.get('min_delta', 0.001)
    )
    
    # 训练历史
    history = {'train_loss': [], 'val_loss': [], 'val_r2': []}
    
    print(f"开始训练模型: {config.get('model_type', 'unknown')}")
    
    for epoch in range(config['epochs']):
        # 训练阶段
        model.train()
        train_losses = []
        
        for batch_features, batch_labels in train_loader:
            batch_features, batch_labels = batch_features.to(device), batch_labels.to(device)
            
            optimizer.zero_grad()
            outputs = model(batch_features)
            loss = criterion(outputs, batch_labels)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            train_losses.append(loss.item())
        
        # 验证阶段
        model.eval()
        val_losses = []
        val_predictions = []
        val_targets = []
        
        with torch.no_grad():
            for batch_features, batch_labels in val_loader:
                batch_features, batch_labels = batch_features.to(device), batch_labels.to(device)
                outputs = model(batch_features)
                loss = criterion(outputs, batch_labels)
                val_losses.append(loss.item())
                val_predictions.extend(outputs.cpu().numpy())
                val_targets.extend(batch_labels.cpu().numpy())
        
        # 计算指标
        train_loss = np.mean(train_losses)
        val_loss = np.mean(val_losses)
        val_r2 = r2_score(val_targets, val_predictions)
        
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['val_r2'].append(val_r2)
        
        # 学习率调度
        if scheduler:
            if isinstance(scheduler, ReduceLROnPlateau):
                scheduler.step(val_loss)
            else:
                scheduler.step()
        
        # 打印进度
        if (epoch + 1) % 10 == 0:
            current_lr = optimizer.param_groups[0]['lr']
            print(f"Epoch {epoch+1}/{config['epochs']}: "
                  f"Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}, "
                  f"Val R²: {val_r2:.4f}, LR: {current_lr:.2e}")
        
        # 早停检查
        if early_stopping(val_loss, model):
            print(f"Early stopping at epoch {epoch+1}")
            break
    
    return model, history

def advanced_training():
    """高级训练主函数"""
    print("开始高级模型训练...")
    
    # 设备设置
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name(0)}")
    
    # 加载预处理后的数据
    X_train = np.load('Result/advanced/train_features.npy')
    y_train = np.load('Result/advanced/train_labels.npy')
    X_test = np.load('Result/advanced/test_features.npy')
    y_test = np.load('Result/advanced/test_labels.npy')
    
    print(f"训练数据: {X_train.shape}, 测试数据: {X_test.shape}")
    
    # 创建数据加载器
    train_dataset = TensorDataset(torch.FloatTensor(X_train), torch.FloatTensor(y_train))
    test_dataset = TensorDataset(torch.FloatTensor(X_test), torch.FloatTensor(y_test))
    
    train_loader = DataLoader(train_dataset, batch_size=128, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=128, shuffle=False)
    
    # 模型配置
    input_dim = X_train.shape[1]
    model_configs = [
        {
            'model_type': 'improved_transformer',
            'd_model': 512,
            'nhead': 16,
            'num_layers': 8,
            'dim_feedforward': 2048,
            'dropout': 0.15,
            'num_residual_blocks': 4,
            'learning_rate': 0.0005,
            'epochs': 150,
            'weight_decay': 0.01,
            'scheduler': 'cosine',
            'loss_type': 'combined',
            'patience': 25
        },
        {
            'model_type': 'deep_mlp',
            'hidden_dims': [1024, 512, 256, 128, 64],
            'dropout': 0.2,
            'learning_rate': 0.001,
            'epochs': 150,
            'weight_decay': 0.01,
            'scheduler': 'cosine',
            'loss_type': 'combined',
            'patience': 25
        },
        {
            'model_type': 'hybrid',
            'd_model': 512,
            'nhead': 16,
            'num_layers': 6,
            'dropout': 0.15,
            'learning_rate': 0.0005,
            'epochs': 150,
            'weight_decay': 0.01,
            'scheduler': 'cosine',
            'loss_type': 'combined',
            'patience': 25
        }
    ]
    
    # 训练多个模型
    trained_models = []
    results = []
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_dir = f"Result/advanced_training_{timestamp}"
    os.makedirs(results_dir, exist_ok=True)
    
    for i, config in enumerate(model_configs):
        print(f"\n{'='*50}")
        print(f"训练模型 {i+1}/{len(model_configs)}: {config['model_type']}")
        print(f"{'='*50}")
        
        # 创建模型
        model = create_model(config['model_type'], input_dim, **{k: v for k, v in config.items() 
                                                                if k not in ['model_type', 'learning_rate', 'epochs', 'weight_decay', 'scheduler', 'loss_type', 'patience']})
        
        # 训练模型
        trained_model, history = train_single_model(model, train_loader, test_loader, config, device)
        
        # 评估模型
        trained_model.eval()
        test_predictions = []
        test_targets = []
        
        with torch.no_grad():
            for batch_features, batch_labels in test_loader:
                batch_features = batch_features.to(device)
                outputs = trained_model(batch_features)
                test_predictions.extend(outputs.cpu().numpy())
                test_targets.extend(batch_labels.numpy())
        
        # 计算指标
        metrics = calculate_metrics(test_predictions, test_targets)
        metrics.update(config)
        results.append(metrics)
        
        print(f"模型 {i+1} 测试结果:")
        print(f"R²: {metrics['R²']:.4f}")
        print(f"RMSE: {metrics['RMSE']:.4f}")
        print(f"PCC: {metrics['PCC']:.4f}")
        
        # 保存模型
        model_path = f"{results_dir}/model_{i+1}_{config['model_type']}.pt"
        torch.save({
            'model_state_dict': trained_model.state_dict(),
            'config': config,
            'metrics': metrics,
            'history': history
        }, model_path)
        
        trained_models.append(trained_model)
    
    # 保存结果
    with open(f"{results_dir}/results.json", 'w') as f:
        json.dump(results, f, indent=2)
    
    # 找到最佳模型
    best_model_idx = max(range(len(results)), key=lambda i: results[i]['R²'])
    best_result = results[best_model_idx]
    
    print(f"\n{'='*50}")
    print("最佳模型结果:")
    print(f"模型类型: {best_result['model_type']}")
    print(f"R²: {best_result['R²']:.4f}")
    print(f"RMSE: {best_result['RMSE']:.4f}")
    print(f"PCC: {best_result['PCC']:.4f}")
    print(f"{'='*50}")
    
    return trained_models, results, results_dir

if __name__ == "__main__":
    trained_models, results, results_dir = advanced_training()
