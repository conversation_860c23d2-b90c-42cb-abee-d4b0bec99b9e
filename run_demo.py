#!/usr/bin/env python3
"""
Kcat预测模型演示脚本
展示最佳模型的使用方法
"""

import os
import sys

def main():
    """主演示函数"""
    print("🧬 Kcat预测模型演示")
    print("=" * 50)
    
    print("\n📋 可用操作:")
    print("1. 查看模型性能和可视化结果")
    print("2. 重新生成散点图和残差图")
    print("3. 查看项目结构")
    print("4. 退出")
    
    while True:
        try:
            choice = input("\n请选择操作 (1-4): ").strip()
            
            if choice == "1":
                print("\n📊 查看模型性能...")
                os.system("python show_plot_results.py")
                
            elif choice == "2":
                print("\n📈 重新生成可视化...")
                os.system("python generate_best_model_plots.py")
                
            elif choice == "3":
                print("\n📁 项目结构:")
                print_project_structure()
                
            elif choice == "4":
                print("\n👋 再见！")
                break
                
            else:
                print("❌ 无效选择，请输入 1-4")
                
        except KeyboardInterrupt:
            print("\n\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")

def print_project_structure():
    """打印项目结构"""
    structure = """
📁 项目结构:
kcat/
├── 📊 kcat_features.tsv                    # 原始数据文件 (393MB)
├── 🔧 data_preprocessing.py                # 数据预处理脚本
├── 📈 generate_best_model_plots.py         # 生成散点图脚本
├── 👁️  show_plot_results.py                 # 显示结果脚本
├── 🚀 run_demo.py                          # 演示脚本 (当前)
├── 📄 训练总结报告.md                       # 训练总结报告
├── 📄 README.md                            # 项目说明
├── 📂 Result/                              # 结果目录
│   ├── 📊 train_features.npy               # 训练特征 (14,434样本×1280维)
│   ├── 📊 train_labels.npy                 # 训练标签
│   ├── 📊 test_features.npy                # 测试特征 (3,609样本×1280维)
│   ├── 📊 test_labels.npy                  # 测试标签
│   ├── 🤖 Transformer_Model/               # 最佳模型目录
│   │   └── 20250629_230536/
│   │       └── best_model.pt               # 最佳模型文件 (R²=0.3438)
│   └── 📈 Best_Model_Plots/                # 可视化结果
│       └── 20250630_105402/
│           ├── scatter_plot.png            # 散点图 (真实值 vs 预测值)
│           ├── residuals_plot.png          # 残差图 (误差分布)
│           └── model_info.json             # 模型详细信息
└── 💻 TransCode/                           # 核心代码
    └── utils/
        ├── transformer_regressor.py        # Transformer模型定义
        ├── dataset.py                      # 数据集类
        ├── metrics.py                      # 评估指标计算
        ├── save_results.py                 # 结果保存和可视化
        ├── logger.py                       # 日志工具
        └── helpers.py                      # 辅助函数

🎯 模型信息:
- 模型类型: Transformer回归器
- 最佳R²: 0.3438 (能解释34.4%的数据变异)
- 特征: smiles_vector + sequence_vector (1280维)
- 任务: 预测蛋白质-底物相互作用的Log10_Kcat_Value

📊 数据统计:
- 总样本: 18,043个蛋白质-底物对
- 训练集: 14,434样本 (80%)
- 测试集: 3,609样本 (20%)
- 特征维度: 1280 (分子向量 + 蛋白质序列向量)

🚀 快速开始:
1. 查看结果: python show_plot_results.py
2. 生成图表: python generate_best_model_plots.py
3. 重新预处理: python data_preprocessing.py
"""
    print(structure)

if __name__ == "__main__":
    main()
