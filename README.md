
# 清理后的项目结构

```
kcat/
├── kcat_features.tsv                    # 原始数据文件
├── data_preprocessing.py                # 数据预处理脚本
├── generate_best_model_plots.py         # 生成散点图脚本
├── show_plot_results.py                 # 显示结果脚本
├── 训练总结报告.md                       # 训练总结报告
├── Result/                              # 结果目录
│   ├── train_features.npy               # 训练特征
│   ├── train_labels.npy                 # 训练标签
│   ├── test_features.npy                # 测试特征
│   ├── test_labels.npy                  # 测试标签
│   ├── Transformer_Model/               # 最佳模型目录
│   │   └── 20250629_230536/
│   │       └── best_model.pt            # 最佳模型文件 (R²=0.3438)
│   └── Best_Model_Plots/                # 可视化结果
│       └── 20250630_105402/
│           ├── scatter_plot.png         # 散点图
│           ├── residuals_plot.png       # 残差图
│           └── model_info.json          # 模型信息
└── TransCode/                           # 核心代码
    └── utils/
        ├── transformer_regressor.py     # Transformer模型定义
        ├── dataset.py                   # 数据集类
        ├── metrics.py                   # 评估指标
        ├── save_results.py              # 结果保存和可视化
        ├── logger.py                    # 日志工具
        └── helpers.py                   # 辅助函数
```

## 使用说明

1. **查看模型性能**: `python show_plot_results.py`
2. **重新生成可视化**: `python generate_best_model_plots.py`
3. **重新预处理数据**: `python data_preprocessing.py`

## 模型信息

- **模型类型**: Transformer回归器
- **最佳R²**: 0.3438
- **特征维度**: 1280 (smiles_vector + sequence_vector)
- **训练样本**: 14,434
- **测试样本**: 3,609
