import json
import torch
from torch.utils.data import DataLoader
from sklearn.model_selection import ParameterGrid
from utils.dataset import NPYDataset
from utils.metrics import calculate_metrics
from utils.save_results import create_results_folder, save_metrics_and_results, plot_and_save
from utils.helpers import save_best_model
from utils.logger import setup_logger
from utils.transformer_regressor import TransformerRegressor
import random
import numpy as np
import os


def train_transformer(model, dataloader, criterion, optimizer, device):
    model.train()
    total_loss = 0
    for inputs, targets in dataloader:
        inputs = inputs.to(device)
        targets = targets.to(device)

        optimizer.zero_grad(set_to_none=True)

        with torch.cuda.amp.autocast(enabled=False):
            outputs = model(inputs)
            loss = criterion(outputs.squeeze(), targets)

        if torch.isnan(loss):
            logger.error("NaN loss encountered, aborting epoch")
            raise ValueError("NaN loss")

        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        optimizer.step()

        total_loss += loss.item()
    return total_loss / len(dataloader)


def evaluate_transformer(model, dataloader, device):
    model.eval()
    predictions, true_labels = [], []
    with torch.no_grad():
        for inputs, targets in dataloader:
            inputs = inputs.to(device).float()
            outputs = model(inputs)

            output_np = outputs.squeeze().cpu().numpy()

            if output_np.ndim == 0:
                predictions.append(output_np.item())
            else:
                predictions.extend(output_np.tolist())
            true_labels.extend(targets.numpy())
    return predictions, true_labels


def set_seed(seed=42):
    torch.manual_seed(seed)
    np.random.seed(seed)
    random.seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


if __name__ == "__main__":
    set_seed(42)

    with open("config_complete.json") as f:
        config = json.load(f)

    train_dataset = NPYDataset(
        config["train_features_file"], config["train_labels_file"])
    test_dataset = NPYDataset(
        config["test_features_file"], config["test_labels_file"])

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name(0)}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")

    train_loader = DataLoader(train_dataset, batch_size=64, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=64, shuffle=False)

    grid = list(ParameterGrid(config["param_grid"]))
    results_folder = create_results_folder(config["results_folder"])
    log_file = f"{results_folder}/experiment.log"
    logger = setup_logger(log_file)

    best_pcc = float("-inf")
    best_metrics = {}
    best_params = {}

    all_results = []
    best_model_global_path = f"{config['results_folder']}/best_model.pt"
    best_model_path = f"{results_folder}/best_model.pt"

    # 创建模型保存目录
    models_dir = f"{results_folder}/models"
    os.makedirs(models_dir, exist_ok=True)

    for idx, params in enumerate(grid):
        logger.info(f"Testing configuration: {params}")

        model = TransformerRegressor(
            input_dim=train_dataset.features.shape[1],
            d_model=params.get("d_model", 64),
            nhead=params.get("nhead", 4),
            num_layers=params.get("num_layers", 2),
            dim_feedforward=params.get("dim_feedforward", 128),
            dropout=params.get("dropout", 0.1)
        ).to(device)

        criterion = torch.nn.MSELoss()
        optimizer = torch.optim.AdamW(
            model.parameters(), lr=params["learning_rate"])

        # best_val_pcc = float("-inf")
        # patience = 80
        # patience_counter = 0

        for epoch in range(params.get("epochs", 20)):
            train_loss = train_transformer(
                model, train_loader, criterion, optimizer, device)
            logger.info(f"Epoch {epoch + 1}: Train Loss = {train_loss:.4f}")

            predictions, true_labels = evaluate_transformer(
                model, test_loader, device)
            metrics = calculate_metrics(predictions, true_labels)
            metrics = {key: float(value) for key, value in metrics.items()}

            logger.info("Evaluation Metrics:")
            for key, val in metrics.items():
                logger.info(f"{key}: {val:.4f}")

            # if metrics["PCC"] > best_val_pcc:
            #     best_val_pcc = metrics["PCC"]
            #     patience_counter = 0
            # else:
            #     patience_counter += 1
            #     if patience_counter >= patience:
            #         logger.info("Early stopping triggered!")
            #         break

        current_result = {**params, **metrics}
        all_results.append(current_result)

        # 保存当前模型
        current_model_path = f"{models_dir}/model_{idx+1:03d}.pt"
        torch.save({
            'model_state_dict': model.state_dict(),
            'params': params,
            'metrics': metrics,
            'epoch': params.get("epochs", 20)
        }, current_model_path)
        logger.info(f"Model saved to {current_model_path}")

        if metrics["PCC"] > best_pcc:
            best_pcc = metrics["PCC"]
            best_params = params.copy()
            best_metrics = metrics
            save_best_model(model, best_params, best_metrics, best_model_path,
                            best_model_global_path, results_folder, logger)
            logger.info("Best model updated and saved.")

    best_model_metrics = {**best_params, **best_metrics}
    save_metrics_and_results(
        all_results, best_model_metrics, results_folder, logger)
    plot_and_save(predictions, true_labels, results_folder, logger)
    logger.info(f"Results saved in {results_folder}")
