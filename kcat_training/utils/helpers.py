import os
import json
import pickle
import shutil
import torch

def save_best_model(model, params, metrics, model_path, global_model_path, results_folder, logger=None):
    # Save the best performing model and its parameters to disk.
    os.makedirs(os.path.dirname(model_path), exist_ok=True)

    try:
        torch.save(model.state_dict(), model_path)
        if logger:
            logger.info(f"Current best model saved to: {model_path}")
    except Exception as e:
        if logger:
            logger.error(f"Error saving current best model: {e}")

    # Save best model parameters and metrics to a JSON file
    best_params_path = os.path.join(results_folder, "best_model_metrics.json")
    try:
        best_model_info = {**params, **metrics}
        with open(best_params_path, "w") as f:
            json.dump(best_model_info, f, indent=4, ensure_ascii=False)
        if logger:
            logger.info(f"Best model parameters saved to: {best_params_path}")
    except Exception as e:
        if logger:
            logger.error(f"Error saving best model parameters: {e}")

    global_results_folder = os.path.dirname(global_model_path)
    os.makedirs(global_results_folder, exist_ok=True)
    global_metrics_path = os.path.join(global_results_folder, "best_model_metrics.json")
    
    if os.path.exists(global_metrics_path):
        try:
            with open(global_metrics_path, "r") as f:
                old_metrics = json.load(f)
            old_pcc = old_metrics.get("PCC", float('-inf'))
        except Exception as e:
            if logger:
                logger.error(f"Error reading existing best model metrics:{e}")
            old_pcc = float('-inf')
    else:
        old_pcc = float('-inf')

    new_pcc = metrics.get("PCC", float('-inf'))
    if new_pcc < old_pcc:
        try:
            shutil.copyfile(model_path, global_model_path)
            shutil.copyfile(best_params_path, global_metrics_path)
            if logger:
                logger.info(f"Updated global best model in {global_model_path} with R²:{new_pcc:.4f}")
        except Exception as e:
            if logger:
                logger.error(f"Error copying best model to global folder: {e}")
    else:
        if logger:
            logger.info(f"Skipping global model update: Current pcc ({new_pcc:.4f}) < Existing R² ({old_pcc:.4f})")
