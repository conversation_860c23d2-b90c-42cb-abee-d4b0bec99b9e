import numpy as np
import torch
from torch.utils.data import Dataset

class NPYDataset(Dataset):
    def __init__(self, features_file, labels_file):
        self.features = np.load(features_file)
        self.labels = np.load(labels_file)

    def __len__(self):
        return len(self.features)

    def __getitem__(self, idx):
        # feature = self.features[idx]
        # label = self.labels[idx]
        feature = torch.from_numpy(self.features[idx]).float()
        label = torch.tensor(self.labels[idx], dtype=torch.float32)
        return feature, label
