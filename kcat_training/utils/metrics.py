from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
import numpy as np
from scipy.stats import pearsonr, spearmanr

def calculate_metrics(predictions, true_labels):
    r2 = r2_score(true_labels, predictions)
    rmse = np.sqrt(mean_squared_error(true_labels, predictions))
    mae = mean_absolute_error(true_labels, predictions)
    pcc, _ = pearsonr(true_labels, predictions)
    scc, _ = spearmanr(true_labels, predictions)
    return {"R²": r2, "RMSE": rmse, "MAE": mae, "PCC": pcc, "SCC": scc}
