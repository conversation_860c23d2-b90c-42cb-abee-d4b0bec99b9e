import os
import json
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime

def create_results_folder(base_dir):
    # Create a timestamped results folder to store experiment outputs.
    # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    timestamp = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    results_folder = os.path.join(base_dir, timestamp)
    os.makedirs(results_folder, exist_ok=True)
    return results_folder

def save_metrics_and_results(all_results, best_model_metrics, results_folder, logger=None):
    # Save all experiment results and best model metrics to JSON files.
    metrics_file = os.path.join(results_folder, "metrics.json")

    try:
        with open(metrics_file, "w") as f:
            json.dump(all_results, f, indent=4, ensure_ascii=False)
        if logger:
            logger.info(f"All model evaluation results saved to: {metrics_file}")
    except Exception as e:
        if logger:
            logger.error(f"Error saving model evaluation results: {e}")

    best_params_file = os.path.join(results_folder, "best_model_metrics.json")
    try:
        with open(best_params_file, "w") as f:
            json.dump(best_model_metrics, f, indent=4, ensure_ascii=False)
        if logger:
            logger.info(f"Best model parameters saved to: {best_params_file}")
    except Exception as e:
        if logger:
            logger.error(f"Error saving best model parameters: {e}")

def plot_and_save(predictions, true_labels, results_folder, logger=None):
    # Generate and save scatter plot of true vs predicted labels and residuals distribution plot.
    scatter_plot_path = os.path.join(results_folder, "scatter_plot.png")
    residuals_plot_path = os.path.join(results_folder, "residuals_plot.png")

    # Create scatter plot (True vs Predicted values)
    plt.scatter(true_labels, predictions, alpha=0.6)
    plt.plot([min(true_labels), max(true_labels)], [min(true_labels), max(true_labels)], color='red', linestyle='--')
    plt.xlabel("True Labels")
    plt.ylabel("Predicted Labels")
    plt.title("True vs Predicted Labels")
    plt.savefig(scatter_plot_path)
    plt.close()

    # Create residuals histogram
    residuals = np.array(true_labels) - np.array(predictions)
    plt.hist(residuals, bins=30, alpha=0.7)
    plt.xlabel("Residuals")
    plt.ylabel("Frequency")
    plt.title("Residuals Distribution")
    plt.savefig(residuals_plot_path)
    plt.close()

    if logger:
        logger.info(f"Scatter plot saved to: {scatter_plot_path}")
        logger.info(f"Residuals distribution plot saved to: {residuals_plot_path}")