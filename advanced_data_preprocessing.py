#!/usr/bin/env python3
"""
高级数据预处理脚本
实施特征标准化、异常值处理、特征工程等改进
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, RobustScaler, PowerTransformer
from sklearn.feature_selection import VarianceThreshold
from sklearn.decomposition import PCA
import matplotlib.pyplot as plt
import seaborn as sns
import os

def analyze_data_distribution(features, labels, save_dir="Result/analysis"):
    """分析数据分布"""
    os.makedirs(save_dir, exist_ok=True)
    
    print("=== 数据分布分析 ===")
    
    # 标签分布
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 3, 1)
    plt.hist(labels, bins=50, alpha=0.7, edgecolor='black')
    plt.title('Log10_Kcat_Value Distribution')
    plt.xlabel('Log10_Kcat_Value')
    plt.ylabel('Frequency')
    
    plt.subplot(1, 3, 2)
    from scipy import stats
    stats.probplot(labels, dist="norm", plot=plt)
    plt.title('Q-Q Plot')
    
    plt.subplot(1, 3, 3)
    plt.boxplot(labels)
    plt.title('Boxplot of Labels')
    plt.ylabel('Log10_Kcat_Value')
    
    plt.tight_layout()
    plt.savefig(f'{save_dir}/label_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 特征统计
    feature_stats = {
        'mean': features.mean(axis=0),
        'std': features.std(axis=0),
        'min': features.min(axis=0),
        'max': features.max(axis=0),
        'zero_ratio': (features == 0).mean(axis=0)
    }
    
    print(f"特征维度: {features.shape[1]}")
    print(f"零值特征比例 > 0.9: {(feature_stats['zero_ratio'] > 0.9).sum()}")
    print(f"低方差特征 (std < 0.01): {(feature_stats['std'] < 0.01).sum()}")
    print(f"标签偏度: {stats.skew(labels):.3f}")
    print(f"标签峰度: {stats.kurtosis(labels):.3f}")
    
    return feature_stats

def remove_outliers(features, labels, method='iqr', factor=1.5):
    """移除异常值"""
    print(f"\n=== 异常值处理 ({method}) ===")
    original_size = len(labels)
    
    if method == 'iqr':
        Q1 = np.percentile(labels, 25)
        Q3 = np.percentile(labels, 75)
        IQR = Q3 - Q1
        lower_bound = Q1 - factor * IQR
        upper_bound = Q3 + factor * IQR
        
        mask = (labels >= lower_bound) & (labels <= upper_bound)
        
    elif method == 'zscore':
        z_scores = np.abs(stats.zscore(labels))
        mask = z_scores < factor
        
    elif method == 'percentile':
        lower_percentile = factor
        upper_percentile = 100 - factor
        lower_bound = np.percentile(labels, lower_percentile)
        upper_bound = np.percentile(labels, upper_percentile)
        mask = (labels >= lower_bound) & (labels <= upper_bound)
    
    filtered_features = features[mask]
    filtered_labels = labels[mask]
    
    removed_count = original_size - len(filtered_labels)
    print(f"移除异常值: {removed_count} ({removed_count/original_size*100:.2f}%)")
    print(f"剩余样本: {len(filtered_labels)}")
    
    return filtered_features, filtered_labels

def feature_engineering(features):
    """特征工程"""
    print("\n=== 特征工程 ===")
    
    # 1. 移除低方差特征
    variance_selector = VarianceThreshold(threshold=0.01)
    features_var_filtered = variance_selector.fit_transform(features)
    removed_features = features.shape[1] - features_var_filtered.shape[1]
    print(f"移除低方差特征: {removed_features}")
    
    # 2. 特征标准化
    scaler = RobustScaler()  # 对异常值更鲁棒
    features_scaled = scaler.fit_transform(features_var_filtered)
    
    # 3. 添加交互特征（选择性地添加一些）
    # 计算特征的平方和平方根
    features_squared = np.square(features_scaled)
    features_sqrt = np.sqrt(np.abs(features_scaled))
    
    # 只选择前100个特征进行交互，避免维度爆炸
    n_interact = min(100, features_scaled.shape[1])
    features_interact = features_scaled[:, :n_interact]
    
    # 添加一些统计特征
    features_stats = np.column_stack([
        features_scaled.mean(axis=1),  # 每个样本的均值
        features_scaled.std(axis=1),   # 每个样本的标准差
        features_scaled.max(axis=1),   # 每个样本的最大值
        features_scaled.min(axis=1),   # 每个样本的最小值
        (features_scaled > 0).sum(axis=1),  # 正值数量
        (features_scaled < 0).sum(axis=1),  # 负值数量
    ])
    
    # 合并所有特征
    features_engineered = np.column_stack([
        features_scaled,
        features_squared[:, :50],  # 只取前50个特征的平方
        features_sqrt[:, :50],     # 只取前50个特征的平方根
        features_stats
    ])
    
    print(f"原始特征维度: {features.shape[1]}")
    print(f"工程后特征维度: {features_engineered.shape[1]}")
    
    return features_engineered, scaler, variance_selector

def advanced_preprocessing(file_path='kcat_features.tsv', test_size=0.2, random_state=42):
    """高级数据预处理主函数"""
    print("开始高级数据预处理...")
    
    # 1. 加载原始数据
    print("加载原始数据...")
    features = np.load('Result/train_features.npy')
    labels = np.load('Result/train_labels.npy')
    test_features = np.load('Result/test_features.npy')
    test_labels = np.load('Result/test_labels.npy')
    
    # 合并训练和测试数据进行统一预处理
    all_features = np.vstack([features, test_features])
    all_labels = np.hstack([labels, test_labels])
    
    print(f"总样本数: {len(all_labels)}")
    
    # 2. 数据分布分析
    feature_stats = analyze_data_distribution(all_features, all_labels)
    
    # 3. 异常值处理
    clean_features, clean_labels = remove_outliers(all_features, all_labels, method='iqr', factor=2.0)
    
    # 4. 特征工程
    engineered_features, scaler, variance_selector = feature_engineering(clean_features)
    
    # 5. 标签变换（如果需要）
    # 检查标签是否需要变换
    from scipy import stats
    skewness = stats.skew(clean_labels)
    if abs(skewness) > 1.0:
        print(f"\n标签偏度较大 ({skewness:.3f})，应用Box-Cox变换")
        # 由于可能有负值，使用Yeo-Johnson变换
        label_transformer = PowerTransformer(method='yeo-johnson')
        clean_labels_transformed = label_transformer.fit_transform(clean_labels.reshape(-1, 1)).flatten()
    else:
        print(f"\n标签分布较好 (偏度: {skewness:.3f})，不需要变换")
        clean_labels_transformed = clean_labels
        label_transformer = None
    
    # 6. 重新划分训练测试集
    X_train, X_test, y_train, y_test = train_test_split(
        engineered_features, clean_labels_transformed, 
        test_size=test_size, random_state=random_state, stratify=None
    )
    
    print(f"\n=== 最终数据统计 ===")
    print(f"训练集: {X_train.shape}")
    print(f"测试集: {X_test.shape}")
    print(f"特征维度: {X_train.shape[1]}")
    
    # 7. 保存处理后的数据
    os.makedirs('Result/advanced', exist_ok=True)
    np.save('Result/advanced/train_features.npy', X_train)
    np.save('Result/advanced/train_labels.npy', y_train)
    np.save('Result/advanced/test_features.npy', X_test)
    np.save('Result/advanced/test_labels.npy', y_test)
    
    # 保存预处理器
    import joblib
    joblib.dump(scaler, 'Result/advanced/scaler.pkl')
    joblib.dump(variance_selector, 'Result/advanced/variance_selector.pkl')
    if label_transformer:
        joblib.dump(label_transformer, 'Result/advanced/label_transformer.pkl')
    
    print("高级预处理完成！数据已保存到 Result/advanced/")
    
    return X_train, X_test, y_train, y_test

if __name__ == "__main__":
    X_train, X_test, y_train, y_test = advanced_preprocessing()
