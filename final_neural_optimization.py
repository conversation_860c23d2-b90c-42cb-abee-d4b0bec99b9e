#!/usr/bin/env python3
"""
最终神经网络优化策略
尝试最先进的神经网络架构和训练技术
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
from sklearn.metrics import r2_score
import matplotlib.pyplot as plt
from torch.optim.lr_scheduler import OneCycleLR
import torch.nn.functional as F

class TabNet(nn.Module):
    """TabNet架构 - 专门为表格数据设计的神经网络"""
    def __init__(self, input_dim, output_dim=1, n_d=64, n_a=64, n_steps=5, gamma=1.3, n_independent=2, n_shared=2, epsilon=1e-15):
        super().__init__()
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.n_d = n_d
        self.n_a = n_a
        self.n_steps = n_steps
        self.gamma = gamma
        self.epsilon = epsilon
        
        # 特征变换器
        self.initial_bn = nn.BatchNorm1d(input_dim)
        
        # 共享层
        self.shared_layers = nn.ModuleList()
        for i in range(n_shared):
            if i == 0:
                self.shared_layers.append(nn.Linear(input_dim, 2 * (n_d + n_a)))
            else:
                self.shared_layers.append(nn.Linear(2 * (n_d + n_a), 2 * (n_d + n_a)))
        
        # 独立层
        self.independent_layers = nn.ModuleList()
        for step in range(n_steps):
            step_layers = nn.ModuleList()
            for i in range(n_independent):
                if i == 0:
                    step_layers.append(nn.Linear(2 * (n_d + n_a), 2 * (n_d + n_a)))
                else:
                    step_layers.append(nn.Linear(2 * (n_d + n_a), 2 * (n_d + n_a)))
            self.independent_layers.append(step_layers)
        
        # 注意力变换器
        self.attention_transformers = nn.ModuleList()
        for step in range(n_steps):
            self.attention_transformers.append(nn.Linear(n_a, input_dim))
        
        # 最终分类器
        self.final_mapping = nn.Linear(n_d, output_dim)
        
    def forward(self, x):
        x = self.initial_bn(x)
        
        # 共享层处理
        for shared_layer in self.shared_layers:
            x = shared_layer(x)
            x = F.glu(x, dim=1)
        
        # 步骤处理
        outputs = []
        for step in range(self.n_steps):
            # 独立层处理
            step_output = x
            for independent_layer in self.independent_layers[step]:
                step_output = independent_layer(step_output)
                step_output = F.glu(step_output, dim=1)
            
            # 分离决策和注意力
            decision_out = step_output[:, :self.n_d]
            attention_out = step_output[:, self.n_d:]
            
            # 注意力机制
            attention = self.attention_transformers[step](attention_out)
            attention = F.softmax(attention, dim=1)
            
            outputs.append(decision_out)
        
        # 聚合输出
        output = sum(outputs) / len(outputs)
        output = self.final_mapping(output)
        
        return output.squeeze(-1)

class ResNetRegressor(nn.Module):
    """ResNet风格的回归器"""
    def __init__(self, input_dim, hidden_dims=[512, 256, 128], dropout=0.3):
        super().__init__()
        
        self.input_layer = nn.Sequential(
            nn.Linear(input_dim, hidden_dims[0]),
            nn.BatchNorm1d(hidden_dims[0]),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 残差块
        self.res_blocks = nn.ModuleList()
        for i in range(len(hidden_dims) - 1):
            self.res_blocks.append(self._make_res_block(hidden_dims[i], hidden_dims[i+1], dropout))
        
        # 输出层
        self.output_layer = nn.Linear(hidden_dims[-1], 1)
        
    def _make_res_block(self, in_dim, out_dim, dropout):
        return nn.Sequential(
            nn.Linear(in_dim, out_dim),
            nn.BatchNorm1d(out_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(out_dim, out_dim),
            nn.BatchNorm1d(out_dim)
        )
    
    def forward(self, x):
        x = self.input_layer(x)
        
        for res_block in self.res_blocks:
            residual = x
            x = res_block(x)
            if x.size(1) == residual.size(1):
                x = x + residual
            x = F.relu(x)
        
        return self.output_layer(x).squeeze(-1)

class WideAndDeepRegressor(nn.Module):
    """Wide & Deep架构"""
    def __init__(self, input_dim, deep_dims=[512, 256, 128], dropout=0.2):
        super().__init__()
        
        # Wide部分（线性）
        self.wide = nn.Linear(input_dim, 1)
        
        # Deep部分
        deep_layers = []
        prev_dim = input_dim
        for dim in deep_dims:
            deep_layers.extend([
                nn.Linear(prev_dim, dim),
                nn.BatchNorm1d(dim),
                nn.ReLU(),
                nn.Dropout(dropout)
            ])
            prev_dim = dim
        
        self.deep = nn.Sequential(*deep_layers)
        self.deep_output = nn.Linear(deep_dims[-1], 1)
        
        # 组合层
        self.combine = nn.Linear(2, 1)
        
    def forward(self, x):
        wide_out = self.wide(x)
        deep_out = self.deep_output(self.deep(x))
        
        combined = torch.cat([wide_out, deep_out], dim=1)
        return self.combine(combined).squeeze(-1)

def train_advanced_neural_network(model, train_loader, val_loader, epochs=200, device='cuda'):
    """训练高级神经网络"""
    model = model.to(device)
    
    # 使用OneCycleLR学习率调度
    optimizer = optim.AdamW(model.parameters(), lr=0.01, weight_decay=0.01)
    scheduler = OneCycleLR(optimizer, max_lr=0.01, epochs=epochs, steps_per_epoch=len(train_loader))
    
    # 使用SmoothL1Loss（Huber Loss）
    criterion = nn.SmoothL1Loss()
    
    best_val_r2 = -float('inf')
    patience = 30
    patience_counter = 0
    
    train_losses = []
    val_r2_scores = []
    
    for epoch in range(epochs):
        # 训练
        model.train()
        epoch_train_loss = 0
        for batch_features, batch_labels in train_loader:
            batch_features, batch_labels = batch_features.to(device), batch_labels.to(device)
            
            optimizer.zero_grad()
            outputs = model(batch_features)
            loss = criterion(outputs, batch_labels)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            scheduler.step()
            
            epoch_train_loss += loss.item()
        
        # 验证
        model.eval()
        val_predictions = []
        val_targets = []
        
        with torch.no_grad():
            for batch_features, batch_labels in val_loader:
                batch_features, batch_labels = batch_features.to(device), batch_labels.to(device)
                outputs = model(batch_features)
                val_predictions.extend(outputs.cpu().numpy())
                val_targets.extend(batch_labels.cpu().numpy())
        
        val_r2 = r2_score(val_targets, val_predictions)
        
        train_losses.append(epoch_train_loss / len(train_loader))
        val_r2_scores.append(val_r2)
        
        if val_r2 > best_val_r2:
            best_val_r2 = val_r2
            patience_counter = 0
            best_model_state = model.state_dict().copy()
        else:
            patience_counter += 1
        
        if (epoch + 1) % 20 == 0:
            current_lr = optimizer.param_groups[0]['lr']
            print(f"Epoch {epoch+1}/{epochs}: Train Loss: {train_losses[-1]:.4f}, Val R²: {val_r2:.4f}, LR: {current_lr:.2e}")
        
        if patience_counter >= patience:
            print(f"Early stopping at epoch {epoch+1}")
            model.load_state_dict(best_model_state)
            break
    
    return model, best_val_r2, train_losses, val_r2_scores

def final_neural_optimization():
    """最终神经网络优化"""
    print("开始最终神经网络优化...")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 加载数据
    X_train = np.load('Result/advanced/train_features.npy')
    y_train = np.load('Result/advanced/train_labels.npy')
    X_test = np.load('Result/advanced/test_features.npy')
    y_test = np.load('Result/advanced/test_labels.npy')
    
    # 创建数据加载器
    train_dataset = TensorDataset(torch.FloatTensor(X_train), torch.FloatTensor(y_train))
    test_dataset = TensorDataset(torch.FloatTensor(X_test), torch.FloatTensor(y_test))
    
    train_loader = DataLoader(train_dataset, batch_size=256, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=256, shuffle=False)
    
    input_dim = X_train.shape[1]
    
    # 测试不同的神经网络架构
    models = {
        'TabNet': TabNet(input_dim, n_d=128, n_a=128, n_steps=7),
        'ResNet': ResNetRegressor(input_dim, hidden_dims=[1024, 512, 256, 128]),
        'WideDeep': WideAndDeepRegressor(input_dim, deep_dims=[1024, 512, 256, 128])
    }
    
    results = {}
    
    for name, model in models.items():
        print(f"\n{'='*50}")
        print(f"训练 {name} 模型")
        print(f"{'='*50}")
        
        trained_model, best_r2, train_losses, val_r2_scores = train_advanced_neural_network(
            model, train_loader, test_loader, epochs=300, device=device
        )
        
        # 最终测试
        trained_model.eval()
        test_predictions = []
        test_targets = []
        
        with torch.no_grad():
            for batch_features, batch_labels in test_loader:
                batch_features = batch_features.to(device)
                outputs = trained_model(batch_features)
                test_predictions.extend(outputs.cpu().numpy())
                test_targets.extend(batch_labels.numpy())
        
        final_r2 = r2_score(test_targets, test_predictions)
        results[name] = final_r2
        
        print(f"{name} 最终测试 R²: {final_r2:.4f}")
        
        # 保存模型
        torch.save({
            'model_state_dict': trained_model.state_dict(),
            'model_type': name,
            'r2_score': final_r2,
            'train_losses': train_losses,
            'val_r2_scores': val_r2_scores
        }, f'Result/final_{name.lower()}_model.pt')
    
    # 集成最终模型
    print(f"\n{'='*50}")
    print("集成最终模型")
    print(f"{'='*50}")
    
    ensemble_predictions = []
    for name, model in models.items():
        model_path = f'Result/final_{name.lower()}_model.pt'
        checkpoint = torch.load(model_path, map_location=device, weights_only=False)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        predictions = []
        with torch.no_grad():
            for batch_features, batch_labels in test_loader:
                batch_features = batch_features.to(device)
                outputs = model(batch_features)
                predictions.extend(outputs.cpu().numpy())
        
        ensemble_predictions.append(predictions)
    
    # 简单平均集成
    final_ensemble_pred = np.mean(ensemble_predictions, axis=0)
    ensemble_r2 = r2_score(test_targets, final_ensemble_pred)
    results['Ensemble'] = ensemble_r2
    
    print(f"\n{'='*50}")
    print("最终结果汇总")
    print(f"{'='*50}")
    
    for name, r2 in results.items():
        print(f"{name}: R² = {r2:.4f}")
    
    best_method = max(results, key=results.get)
    best_r2 = results[best_method]
    
    print(f"\n最佳方法: {best_method}")
    print(f"最佳R²: {best_r2:.4f}")
    
    if best_r2 >= 0.6:
        print("🎉 成功达到R² >= 0.6的目标！")
    else:
        print(f"⚠️  距离R² = 0.6还差 {0.6 - best_r2:.4f}")
        
        # 数据质量分析
        print(f"\n{'='*50}")
        print("数据质量分析结论")
        print(f"{'='*50}")
        print("基于所有尝试的方法，可能的原因：")
        print("1. 特征与目标变量的内在相关性较低（最高相关性仅0.199）")
        print("2. 数据中可能存在较大的噪声或测量误差")
        print("3. 可能需要额外的领域知识来构造更有效的特征")
        print("4. 蛋白质-底物相互作用的复杂性可能超出了当前特征的表达能力")
        print("5. 可能需要更多样本或更高质量的数据")
    
    return results, best_method, best_r2

if __name__ == "__main__":
    results, best_method, best_r2 = final_neural_optimization()
